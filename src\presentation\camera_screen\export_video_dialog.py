from PySide6.QtCore import Qt
from PySide6.QtWidgets import QSizePolicy, QWidget, QHBoxLayout
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtCore import QUrl
from src.common.controller.main_controller import main_controller
from src.common.controller.controller_manager import controller_manager
from src.common.threads.sub_thread import SubThread
from src.common.model.camera_model import CameraModel,camera_model_manager
from src.common.widget.dialogs.base_dialog import NewBaseDialog
from src.common.qml.models.export_video_data import ExportVideoData
from src.common.threads.sub_thread import SubThread
import requests
from urllib.parse import urlparse
import logging
import os
logger = logging.getLogger(__name__)

class ExportVideoDialog(NewBaseDialog):
    def __init__(self, parent=None, exportVideoData:ExportVideoData = None):
        self.exportVideoData = exportVideoData
        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.quick_widget = QQuickWidget()
        self.quick_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)   
        self.quick_widget.engine().rootContext().setContextProperty("exportVideoData", self.exportVideoData)
        self.quick_widget.setSource(QUrl("qrc:src/common/qml/videoplayback/ExportVideo.qml"))
        self.quick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)

        layout.addWidget(self.quick_widget) # Cưỡng ép lấy focus khi khởi tạo
        widget_main = QWidget()
        widget_main.setLayout(layout)
        super().__init__(parent, title=self.tr("EXPORT VIDEO"), content_widget=widget_main,
                         width_dialog=600, max_height_dialog=300)
        self.setObjectName("ExportVideo")
        self.save_update_signal.connect(self.update_clicked)

    def update_clicked(self):
        print(f'update_clicked = {self.exportVideoData.filePath, self.exportVideoData.fileName}')
        self.exportVideoData.filePathWarn = True if self.exportVideoData.filePath == "" else False
        self.exportVideoData.fileNameWarn = True if self.exportVideoData.fileName == "" else False
        if self.exportVideoData.filePath == "" or self.exportVideoData.fileName == "":
            return
        filename = self.exportVideoData.fileName
        def callback(data):
            try:
                if data is not None:
                    def process():
                        # Lấy đường dẫn folder lưu file
                        folder_path = self.exportVideoData.filePath if hasattr(self, 'exportVideoData') and hasattr(self.exportVideoData, 'filePath') else ""
                        if not os.path.isdir(folder_path):
                            main_controller.show_message_dialog(None, "EXPORT_VIDEO_ERROR")
                        urls = data["data"].get("urls",[])
                        count = 0
                        for url in urls:
                            path = urlparse(url).path
                            # filename = path.split("/")[-1]
                            # if '.' in filename:
                            #     name, ext = filename.rsplit('.', 1)
                            #     new_filename = f"{name}_{count}.{ext}"
                            # else:
                            #     new_filename = filename + f"_{count}"
                            new_filename = filename + f"_{count}" + ".mp4"
                            # Ghép đường dẫn folder và tên file
                            save_path = os.path.join(folder_path, new_filename) if folder_path else new_filename
                            with requests.get(url, stream=True) as r:
                                r.raise_for_status()
                                with open(save_path, "wb") as f:
                                    for chunk in r.iter_content(chunk_size=8192):
                                        if chunk:  # filter out keep-alive new chunks
                                            f.write(chunk)
                            count += 1
                            print("Download completed:", save_path)
                        main_controller.show_message_dialog(None, "EXPORT_VIDEO")
                        return True
                
                    subThread = SubThread(parent=main_controller.list_parent["CameraScreen"],target=process)
                    subThread.start()
                else:
                    main_controller.show_message_dialog(None, "EXPORT_VIDEO_ERROR")

            except Exception as e:
                logger.error(f'handle_export_video error: {e}')
                main_controller.show_message_dialog(None, "EXPORT_VIDEO_ERROR")
        def process_export_video():
            cameraModel:CameraModel = camera_model_manager.get_camera_model(id = self.exportVideoData.data.get("cameraId",None))
            controller = controller_manager.get_controller(server_ip=cameraModel.get_property("server_ip"))
            if controller:
                response = controller.api_client.export_video(data = self.exportVideoData.data)
                return response

        subThread = SubThread(parent=main_controller.list_parent["CameraScreen"],target=process_export_video,callback = callback)
        subThread.start()
        self.close()
    # def closeEvent(self, event):
    #     self.selectCamerasController.closeChanged.emit()
    #     super().closeEvent(event)
