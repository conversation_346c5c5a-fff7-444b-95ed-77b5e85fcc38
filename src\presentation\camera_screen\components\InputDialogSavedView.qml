import QtQuick 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: newSavedViewDialog
    width: 340
    height: 180
    radius: 12
    color: backgroundColor
    border.color: textx
    border.width: 1.5
    visible: false
    anchors.centerIn: parent
    signal accepted(string inputText)
    signal rejected()
    property alias inputText: nameInput.text
    property color textx: gridModel ? gridModel.get_color_theme_by_key("text") : "#888"
    property color primary: gridModel ? gridModel.get_color_theme_by_key("primary") : "#888"
    property color text_same_bg: gridModel ? gridModel.get_color_theme_by_key("text_same_bg") : "#888"
    property color cell_item: gridModel ? gridModel.get_color_theme_by_key("cell_item") : "#888"

    Connections {
        target: gridModel
        function onThemeChanged() {
            backgroundColor = gridModel.get_color_theme_by_key("main_background")
            textx = gridModel.get_color_theme_by_key("text")
            primary = gridModel.get_color_theme_by_key("primary")
            text_same_bg = gridModel.get_color_theme_by_key("text_same_bg")
            cell_item = gridModel.get_color_theme_by_key("cell_item")
        }
    }
    Column {
        anchors.centerIn: parent
        spacing: 18
        width: parent.width - 40

        // // Header with icon and title
        // Row {
        //     spacing: 8
        //     anchors.horizontalCenter: parent.horizontalCenter
        //     Image {
        //         source: "qrc:/icons/warning.svg" // Đổi thành icon bạn có
        //         width: 24; height: 24
        //     }
        //     Text {
        //         text: "Điền tê"
        //         font.bold: true
        //         font.pixelSize: 18
        //         color: "#222"
        //     }
        // }

        // Nội dung
        TextField {
            id: nameInput
            placeholderText: qsTr("Press screen name to save...")
            width: parent.width
            color: textx
            font.pixelSize: 14
            focus: true
            background: Rectangle {
                color: backgroundColor
                radius: 8
                border.color: textx
                border.width: 1
            }
            placeholderTextColor: textx
            padding: 8
            KeyNavigation.tab: createBtn
        }
        Keys.onReturnPressed: {
            // Chỉ gọi khi dialog đang visible
            if (newSavedViewDialog.visible) {
                newSavedViewDialog.visible = false
                newSavedViewDialog.accepted(nameInput.text)
            }
        }
        // Nếu muốn hỗ trợ cả phím Enter trên numpad:
        Keys.onEnterPressed: {
            if (newSavedViewDialog.visible) {
                newSavedViewDialog.visible = false
                newSavedViewDialog.accepted(nameInput.text)
            }
        }
        // Nút
        Row {
            spacing: 16
            anchors.horizontalCenter: parent.horizontalCenter

            Button {
                id: createBtn
                text: qsTr("Create")
                width: 120
                height: 40
                background: Rectangle {
                    color: createBtn.down
                        ? primary
                        : (createBtn.hovered || createBtn.activeFocus
                            ? primary
                            : primary)
                    border.color: createBtn.activeFocus
                        ? text_same_bg
                        : primary
                    border.width: 1
                    radius: 8
                }
                contentItem: Text {
                    text: createBtn.text
                    color: text_same_bg
                    font.pixelSize: 14
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    anchors.fill: parent
                }
                focusPolicy: Qt.StrongFocus
                KeyNavigation.tab: cancelBtn
                KeyNavigation.backtab: nameInput
                onClicked: {
                    newSavedViewDialog.visible = false
                    newSavedViewDialog.accepted(nameInput.text)
                }
            }
            Button {
                id: cancelBtn
                text: qsTr("Cancel")
                width: 120
                height: 40
                background: Rectangle {
                    color: cancelBtn.down
                        ? "transparent"
                        : (cancelBtn.hovered || cancelBtn.activeFocus
                            ? cell_item
                            : "transparent")
                    border.color: cancelBtn.activeFocus
                        ? text_same_bg
                        : textx
                    border.width: 1
                    radius: 8
                }
                contentItem: Text {
                    text: cancelBtn.text
                    color: textx
                    font.pixelSize: 14
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                focusPolicy: Qt.StrongFocus
                KeyNavigation.tab: nameInput
                KeyNavigation.backtab: createBtn
                onClicked: {
                    newSavedViewDialog.visible = false
                    newSavedViewDialog.rejected()
                }
            }
        }
    }
}
