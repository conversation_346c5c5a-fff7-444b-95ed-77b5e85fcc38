import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import models 1.0

/**
 * GridItemButtonControls.qml - Quản lý các nút điều khiển cho GridItem
 *
 * CHỨC NĂNG CHÍNH:
 * - Close, fullscreen, minimize buttons
 * - PTZ control panels
 * - Zoom in/out buttons
 * - Control visibility logic
 */
Item {
    id: root
    anchors.fill: parent


    // ✅ CONTENT BOUNDS: Properties for constraining buttons within actual content area
    property real contentBoundsX: 0
    property real contentBoundsY: 0
    property real contentBoundsWidth: parent.width
    property real contentBoundsHeight: parent.height

    // 1. Properties
    property var gridItem: null  // Reference đến GridItem parent
    property string itemType: "camera"  // Loại item (camera, map, event)
    property bool isDarkTheme: gridItem ? gridItem.isDarkTheme : true
    property bool isSelected: gridItem ? gridItem.isSelected : false
    property bool isMaximized: gridItem && gridItem.itemData ? gridItem.itemData.fullscreen : false
    property bool isHovered: gridItem ? gridItem.isHovered : false
    property bool isFocusMap: gridItem && gridItem.isFocusMap ? gridItem.isFocusMap : false
    property bool showControls: isHovered || isSelected || isMaximized || hasActivePtz || isFocusMap

    // ✅ NEW: Check if any PTZ mode is active to keep buttons visible
    property bool hasActivePtz: {
        if (!gridItem || !gridItem.itemData || itemType !== "camera") return false
        return gridItem.itemData.isPtzActive || gridItem.itemData.isPtz3dActive || gridItem.itemData.isDragZoomActive
    }
    // ✅ NEW: Video wall properties
    // property bool showControlButtons: gridItem ? gridItem.showControlButtons : true
    property bool isVirtualGrid: gridItem ? gridItem.isVirtualGrid : false

    // ✅ NEW: Signals for button actions
    signal closeButtonClicked(var item)
    signal maximizeButtonClicked(var item)
    signal changeFocused()
    signal changeActived(string ptzType)

    property bool isFullscreen: gridItem && gridItem.itemData ? gridItem.itemData.fullscreen : false

    // 2. Button properties
    property int minButtonSize: 12   // Kích thước tối thiểu cho button
    property int maxButtonSize: 42  // Kích thước tối đa cho button
    property int buttonSize: Math.max(minButtonSize, Math.min(contentBoundsWidth * 0.08, maxButtonSize))  // Scale theo content bounds width

    property int minIconSize: 6     // Kích thước tối thiểu cho icon
    property int maxIconSize: 20    // Kích thước tối đa cho icon
    property int iconSize: Math.max(minIconSize, Math.min(buttonSize * 0.6, maxIconSize))    // 60% của buttonSize, min 6px, max 20px
    
    property int minSpacing: 2      // Khoảng cách tối thiểu giữa các button
    property int maxSpacing: 8      // Khoảng cách tối đa giữa các button
    property int buttonSpacing: Math.max(minSpacing, Math.min(contentBoundsWidth * 0.02, maxSpacing)) // Scale theo content bounds width
    
    property int minMargin: 4       // Margin tối thiểu
    property int maxMargin: 18      // Margin tối đa
    property int buttonMargin: Math.max(minMargin, Math.min(contentBoundsWidth * 0.04, maxMargin)) // Scale theo content bounds width

    // Border radius tính toán
    property int minRadius: 2       // Border radius tối thiểu
    property int maxRadius: 8       // Border radius tối đa
    property int buttonRadius: Math.max(minRadius, Math.min(buttonSize * 0.25, maxRadius))  // 25% của buttonSize, min 4px, max 8px

    property string icon_close: gridModel.get_image_theme_by_key("icon_close")
    property string icon_expand_camera: gridModel.get_image_theme_by_key("expand_camera")
    property string icon_shrink_camera: gridModel.get_image_theme_by_key("shrink_camera")
    property string icon_rotate: gridModel.get_image_theme_by_key("rotate_camera")// ✅ FIX: Icon cho nút rotate camera
    property string icon_ptz: gridModel.get_image_theme_by_key("icon_ptz")
    property string icon_ptz_arrow: gridModel.get_image_theme_by_key("icon_ptz_arrow")
    property string icon_drag_zoom: gridModel.get_image_theme_by_key("icon_drag_zoom")
    property color primaryColor: gridModel.get_color_theme_by_key("primary")

    Connections {
        target: gridModel
        function onThemeChanged() {
            icon_close = gridModel.get_image_theme_by_key("icon_close")
            icon_expand_camera = gridModel.get_image_theme_by_key("expand_camera")
            icon_shrink_camera = gridModel.get_image_theme_by_key("shrink_camera")
            icon_rotate = gridModel.get_image_theme_by_key("rotate_camera")
            icon_ptz_arrow = gridModel.get_image_theme_by_key("icon_ptz_arrow")
            icon_ptz = gridModel.get_image_theme_by_key("icon_ptz")
            icon_drag_zoom = gridModel.get_image_theme_by_key("icon_drag_zoom")
            primaryColor = gridModel.get_color_theme_by_key("primary")
        }

    }
    
    // Control buttons container
    Row {
        id: controlButtonsRow
        objectName: "controlButtonsRow"  // ✅ FIX: Add objectName for PTZ controls to find this component

        // ✅ POSITION WITHIN CONTENT BOUNDS: Use content bounds for positioning
        x: root.contentBoundsX + (root.contentBoundsWidth - width) / 2
        y: root.contentBoundsY + root.contentBoundsHeight - height - Math.max(2, Math.min(root.contentBoundsHeight * 0.05, 10))

        spacing: buttonSpacing
        visible: {
            return showControls
        }  // Show when hovering AND video wall allows



        // Drag to zoom PTZ button - CAMERA ONLY
        Rectangle {
            id: dragZoomButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            property bool isActive: gridItem && gridItem.itemData ? gridItem.itemData.isDragZoomActive : false
            property bool isPressed: false
            color: isPressed ? "rgba(255,255,255,0.3)" : (isActive ? primaryColor : Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2))
            Behavior on color { ColorAnimation { duration: 100 } }
            visible: itemType === "camera" && gridItem && gridItem.itemData ? gridItem.itemData.ptz3DSupport : false 

            Image {
                anchors.centerIn: parent
                source: icon_drag_zoom
                width: dragZoomButton.isActive ? buttonSize - 4 : buttonSize
                height: dragZoomButton.isActive ? buttonSize - 4 : buttonSize
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
                Behavior on width { NumberAnimation { duration: 120 } }
                Behavior on height { NumberAnimation { duration: 120 } }
            }

            MouseArea {
                id: dragZoomArea
                anchors.fill: parent
                onPressed: dragZoomButton.isPressed = dragZoomButton.isActive ? true : false
                onReleased: {
                    dragZoomButton.isPressed = false
                    if (containsMouse) changeActived("dragZoom")
                }
            }
            onIsActiveChanged: if (!isActive) dragZoomButton.isPressed = false
        }

        // PTZ button - CAMERA ONLY (ẩn nếu chỉ có zoom)
        Rectangle {
            id: ptzButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            property bool isActive: gridItem && gridItem.itemData ? gridItem.itemData.isPtzActive : false
            property bool isPressed: false
            color: isPressed ? "rgba(255,255,255,0.3)" : (isActive ? primaryColor : Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2))
            Behavior on color { ColorAnimation { duration: 100 } }
            visible: itemType === "camera" && gridItem && gridItem.itemData ? gridItem.itemData.onvifSupported : false

            Image {
                anchors.centerIn: parent
                source: icon_ptz
                width: ptzButton.isActive ? buttonSize - 4 : buttonSize
                height: ptzButton.isActive ? buttonSize - 4 : buttonSize
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
                Behavior on width { NumberAnimation { duration: 120 } }
                Behavior on height { NumberAnimation { duration: 120 } }
            }

            MouseArea {
                id: ptzArea
                anchors.fill: parent
                onPressed: ptzButton.isPressed = ptzButton.isActive ? true : false
                onReleased: {
                    ptzButton.isPressed = false
                    if (containsMouse) changeActived("ptz")
                }
            }
            onIsActiveChanged: if (!isActive) ptzButton.isPressed = false
        }

        // 3D PTZ button - CAMERA ONLY (ẩn nếu chỉ có zoom)
        Rectangle {
            id: ptz3dButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            property bool isActive: gridItem && gridItem.itemData ? gridItem.itemData.isPtz3dActive : false
            property bool isPressed: false
            color: isPressed ? "rgba(255,255,255,0.3)" : (isActive ? primaryColor : Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2))
            Behavior on color { ColorAnimation { duration: 100 } }
            visible: itemType === "camera" && gridItem && gridItem.itemData ? gridItem.itemData.ptzSupported : false 

            Image {
                anchors.centerIn: parent
                source: icon_ptz_arrow
                width: ptz3dButton.isActive ? buttonSize - 4 : buttonSize
                height: ptz3dButton.isActive ? buttonSize - 4 : buttonSize
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
                Behavior on width { NumberAnimation { duration: 120 } }
                Behavior on height { NumberAnimation { duration: 120 } }
            }

            MouseArea {
                id: ptz3dArea
                anchors.fill: parent
                onPressed: ptz3dButton.isPressed = ptz3dButton.isActive ? true : false
                onReleased: {
                    ptz3dButton.isPressed = false
                    if (containsMouse) changeActived("ptz3d")
                }
            }
            onIsActiveChanged: if (!isActive) ptz3dButton.isPressed = false
        }

        // Rotate button - CAMERA ONLY
        // Rectangle {
        //     id: rotateButton
        //     width: buttonSize
        //     height: buttonSize
        //     radius: buttonRadius
        //     visible: itemType === "camera"
        //     color: "transparent"

        //     property bool isPressed: false
        //     property real rotationSpeed: 2.0  // degrees per timer tick

        //     // Smooth color transitions
        //     Behavior on color {
        //         ColorAnimation { duration: 100 }
        //     }

        //     Image {
        //         anchors.centerIn: parent
        //         source: icon_rotate
        //         width: buttonSize
        //         height: buttonSize
        //         fillMode: Image.PreserveAspectFit
        //         // ✅ HIGH-DPI: Render at 2x resolution for crisp icons
        //         sourceSize: Qt.size(width * 2, height * 2)
        //         smooth: true
        //         antialiasing: true

        //         // Visual feedback rotation
        //         rotation: rotateButton.isPressed ? 45 : 0
        //         Behavior on rotation {
        //             RotationAnimation {
        //                 duration: 150
        //                 direction: RotationAnimation.Clockwise
        //             }
        //         }
        //     }

        //     // ✅ NEW: Continuous rotation timer
        //     Timer {
        //         id: rotationTimer
        //         interval: 16  // ~60 FPS
        //         repeat: true
        //         running: rotateButton.isPressed

        //         onTriggered: {
        //             if (gridItem && gridItem.itemData) {
        //                 var currentRotation = gridItem.itemData.rotation || 0
        //                 var newRotation = (currentRotation + rotateButton.rotationSpeed) % 360
        //                 gridItem.itemData.rotation = newRotation
        //             }
        //         }
        //     }

        //     MouseArea {
        //         id: rotateArea
        //         anchors.fill: parent

        //         onPressed: {
        //             rotateButton.isPressed = true
        //             console.log("🔄 [ROTATE_BUTTON] Hold to rotate started!")
        //         }

        //         onReleased: {
        //             rotateButton.isPressed = false
        //             rotateButton.color = rotateArea.containsMouse ? "rgba(255, 255, 255, 0.1)" : "transparent"

        //             if (gridItem && gridItem.itemData) {
        //                 console.log("🔄 [ROTATE_BUTTON] Hold to rotate stopped at", gridItem.itemData.rotation, "degrees")
        //             }
        //         }

        //         onExited: {
        //             if (!rotateButton.isPressed) {
        //                 rotateButton.color = "transparent"
        //             }
        //         }
        //     }
        // }

        // Focus map button
        Rectangle {
            id: focusButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            color: "transparent"
            visible: itemType === "map" ? true : false

            property bool isPressed: false
            property bool isActive: false

            Behavior on color {
                ColorAnimation { duration: 100 }
            }

            Image {
                id: focusIcon
                anchors.centerIn: parent
                source: icon_ptz_arrow
                width: focusButton.isActive ? buttonSize -4 : buttonSize
                height: focusButton.isActive ? buttonSize -4 : buttonSize
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width, height)
            }

            MouseArea {
                id: focusArea
                anchors.fill: parent
                onPressed: {
                    focusButton.isPressed = true
                    focusButton.color = "rgba(255, 255, 255, 0.3)"
                }
                onReleased: {
                    focusButton.isPressed = false
                    focusButton.color = focusArea.containsMouse ? primaryColor : Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2)
                    focusButton.isActive = !focusButton.isActive
                }
                onClicked: {
                    changeFocused()
                }
            }
        }

        // // Focus map button
        // Rectangle {
        //     id: focusButton
        //     width: buttonSize
        //     height: buttonSize
        //     radius: buttonRadius
        //     color: "transparent"
        //     visible: itemType === "map" ? true : false

        //     property bool isPressed: false

        //     Behavior on color {
        //         ColorAnimation { duration: 100 }
        //     }

        //     Image {
        //         anchors.centerIn: parent
        //         source: icon_ptz_arrow
        //         width: buttonSize
        //         height: buttonSize
        //         fillMode: Image.PreserveAspectFit
        //         sourceSize: Qt.size(width, height)
        //     }

        //     MouseArea {
        //         id: focusArea
        //         anchors.fill: parent
        //         onPressed: {
        //             focusButton.isPressed = true
        //             focusButton.color = "rgba(255, 255, 255, 0.3)"
        //         }
        //         onReleased: {
        //             focusButton.isPressed = false
        //             focusButton.color = focusArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
        //         }
        //         onClicked: {
        //             mapFocus = !mapFocus
        //             changeFocused(mapFocus)
        //         }
        //     }
        // }

        // Maximize button
        Rectangle {
            id: maximizeButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            color: "transparent"
            visible: gridModel ? !(gridModel.isSingleGridItem && gridModel.rows === 1 && gridModel.columns === 1) : true  // Ẩn khi chỉ có 1 item và 1x1

            property bool isPressed: false

            Behavior on color {
                ColorAnimation { duration: 100 }
            }

            Image {
                anchors.centerIn: parent
                source: {
                    // Use individual fullscreen state
                    if (gridItem && gridItem.itemData && gridItem.itemData.fullscreen) {
                        return icon_shrink_camera
                    } else {
                        return icon_expand_camera
                    }
                }
                width: buttonSize
                height: buttonSize
                fillMode: Image.PreserveAspectFit
                // ✅ HIGH-DPI: Render at 2x resolution for crisp icons
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
            }

            MouseArea {
                id: maximizeArea
                anchors.fill: parent
                onPressed: {
                    maximizeButton.isPressed = true
                    maximizeButton.color = "rgba(255, 255, 255, 0.3)"
                }
                onReleased: {
                    maximizeButton.isPressed = false
                    maximizeButton.color = maximizeArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
                }
                onClicked: {
                    // console.log("[GridItemButtonControls] 🔍 [MAXIMIZE] Maximize button clicked!")

                    // ✅ NEW: Emit signal instead of hardcoded logic
                    maximizeButtonClicked(gridItem)
                }
            }
        }

        // Map Move button - MAP ONLY
        Rectangle {
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            visible: (itemType === "map2d" || itemType === "digitalMap") && gridItem
            color: {
                var isMapMoveActive = gridItem && gridItem.isMapMoveMode ? gridItem.isMapMoveMode : false
                return isMapMoveActive ? "rgba(0, 120, 215, 0.3)" : "transparent"
            }

            // Image {
            //     anchors.centerIn: parent
            //     source: isDarkTheme ? "qrc:src/assets/camera_stream/icon_move.svg" : "qrc:src/assets/camera_stream/icon_move.svg"
            //     width: buttonSize
            //     height: buttonSize
            //     fillMode: Image.PreserveAspectFit
            //     // ✅ HIGH-DPI: Render at 2x resolution for crisp icons
            //     sourceSize: Qt.size(width * 2, height * 2)
            //     smooth: true
            //     antialiasing: true
            // }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    if (gridItem && gridItem.toggleMapMoveMode) {
                        gridItem.toggleMapMoveMode()
                    }
                }
            }
        }

        // Close button - ALL TYPES
        Rectangle {
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            color: "transparent"

            Image {
                anchors.centerIn: parent
                source: icon_close
                width: buttonSize
                height: buttonSize
                fillMode: Image.PreserveAspectFit
                // ✅ HIGH-DPI: Render at 2x resolution for crisp icons
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
            }

            MouseArea {
                id: closeArea
                anchors.fill: parent
                onClicked: {
                    // ✅ NEW: Emit signal instead of hardcoded logic
                    closeButtonClicked(gridItem)
                }
            }
        }
    }

    // Component initialization
    Component.onCompleted: {
        // console.log("[GridItemButtonControls] Component completed - ButtonControls initialized successfully")
        // console.log("[GridItemButtonControls] Initial gridItem:", gridItem ? "exists" : "null")
        // console.log("[GridItemButtonControls] Initial itemType:", itemType)
        // console.log("[GridItemButtonControls] Initial showControls:", showControls)
        // console.log("[GridItemButtonControls] Initial visibility:", visible)
    }

    // Watch for gridItem changes
    onGridItemChanged: {
        // console.log("[GridItemButtonControls] GridItem changed:", gridItem ? "exists" : "null")
        if (gridItem) {
            // console.log("[GridItemButtonControls] GridItem properties - itemType:", gridItem.itemType, "row/col:", "(" + gridItem.gridRow + "," + gridItem.gridCol + ")")
            // console.log("[GridItemButtonControls] GridItem camera_id:", gridItem.camera_id)
            // console.log("[GridItemButtonControls] GridItem isHovered:", gridItem.isHovered)
        }
    }

    // Watch for showControls changes - REMOVED: Duplicate handler

    // Watch for visibility changes
    onVisibleChanged: {
        // console.log("🎮 [BUTTON_CONTROLS] Visibility changed:", visible)
        // console.log("🎮 [BUTTON_CONTROLS] isHovered:", isHovered, "isSelected:", isSelected, "isMaximized:", isMaximized)
        // if (gridItem) {
        //     console.log("🎮 [BUTTON_CONTROLS] gridItem.isHovered:", gridItem.isHovered)
        // }
    }

    onIsHoveredChanged: {
        // console.log("🎮 [BUTTON_CONTROLS] isHovered changed:", isHovered)
        // console.log("🎮 [BUTTON_CONTROLS] showControls:", showControls, "showControlButtons:", showControlButtons)
        // console.log("🎮 [BUTTON_CONTROLS] gridItem.itemData.supportsPTZ():", gridItem.itemData.supportsPTZ())
    }

    onShowControlsChanged: {
        // console.log("🎮 [BUTTON_CONTROLS] showControls changed:", showControls)
        // console.log("🎮 [BUTTON_CONTROLS] Final visibility should be:", showControls && showControlButtons)
        // console.log("🎮 [BUTTON_CONTROLS] hasActivePtz:", hasActivePtz)
        // if (gridItem && gridItem.itemData) {
        //     console.log("🎮 [BUTTON_CONTROLS] PTZ states - ptz:", gridItem.itemData.isPtzActive, "ptz3d:", gridItem.itemData.isPtz3dActive, "dragZoom:", gridItem.itemData.isDragZoomActive)
        // }
    }

    onHasActivePtzChanged: {
        // console.log("🎮 [BUTTON_CONTROLS] hasActivePtz changed:", hasActivePtz)
    }
}
