from enum import Enum
from PySide6.QtGui import QFont

class Theme(Enum):
    LIGHT = 0
    DARK = 1


class Style:
    class Font:
        arial = "Arial"

    class Size:
        caption = 12
        body = 14
        body_strong = 16
        body_large = 18
        subtitle = 20
        title = 28
        title_large = 40
        display = 68
        border_radius = 4
        common_button_padding = '4px 16px 4px 16px'

    class PrimaryColor:
        text = "#2B2A3A"
        test = "#6e0b0b"
        white = "#FFFFFF" # fixed
        white_2 = "#F7F0F7" # fixed
        white_3 = "#EEEEEE" # fixed
        black = "#000000"
        default = "efefef"
        primary = "#696a6c" # fixed
        green = '#1CD1A1' # fixed
        primary_hover = "#3F3F87" # fixed
        primary_pressed = "#3F3F87" # fixed
        secondary = "#2B2A3A" # fixed
        background = "#181824"
        on_background = "#2B2A3A"
        on_hover = "#656475"
        on_hover_primary = "#3F3F87" # fixed
        on_hover_secondary = "#656475"
        on_hover_button = "#3F3F87"
        hover_button_toolbar = "#656475"
        background_item = "#363546"
        background_item_off = "#2F2E3C"
        background_refresh_button = "#4C9008"
        background_delete_button = "#B5122E"
        hover_refresh_button = "#448601"
        hover_delete_button = "#7E0A0A"
        connect_btn = "#75FE8C"
        # Text color
        text_black = "#2B2A3A"
        text_unselected = "#656475"
        white_2 = "#F7F0F7"
        text_place_holder = "#656475"
        text_disable = "#656475"
        disable_color = "#656475"
        text_on_primary = '#EEEEEE'
        text_not_select = "#F7F0F7"
        text_note = '#575757'
        # border and stroke
        border_line_edit = "#F7F0F7"
        border_item = "#979797"
        divider = "#656475"
        border_line_edit_not_focus = "#656475" # fixed
        # Button
        button_second_background = "#656475" # fixed
        button_primary_background = "#696a6c"
        button_disable_background = "#242424"
        # Menu
        menu_title = "#A5A5A5"

        available = "#48DC6B"
        unavailable = "#CC5051"
        status_appear = "rgba(61, 173, 254, 1)"
        status_checkin = "rgba(19, 172, 25, 1)"
        status_checkout = "rgba(204, 80, 81, 1)"
        text_camera_name = "rgba(255, 255, 255, 0.35)"
        # divider = "rgba(42, 43, 50, 1)"
        background_search_bar_event = "rgba(2, 2, 3, 0.45)"
        text_search_bar_event = "rgba(255, 255, 255, 0.35)"
        status_appear_dialog = "#41A0FA"
        # event_number_color ='rgba(255, 145, 94, 1)'
        event_number_color = "#FF915E"
        button_color = "#3F3F87"
        button_disable = "#DFE0E4"
        border_button = "#5C687F"
        error = "#B5122E"
        pulse_toggle_color = "#FFFFFF"
        background_warning = "#CCCCD1"
        transparent = "rgba(0, 0, 0, 0)"
        button_upload = "#3388DC"
        background_dialog = "#FFFFFF"
        background_box = "#E99899"
        background_box_hover = "#CC5051"
        server_connected = "#1CD1A1"
        widget_disable = "3A3A3A"
        text = "#2B2A3A"  # Using the same color as text_black for default text color

    class StyleSheet:
        default_style_vms = f"""
                            font-size: 14px;
                            color: black;
                            """
        font_text_caption = "font-size: 14px;"
        font_text_body = "font-size: 14px;"
        font_text_body_strong = " font-weight: bold;"
        font_text_body_large = "font-size: 18px;"
        font_text_subtitle = "font-size: 20px;"
        font_text_title = "font-size: 28px;"
        font_text_title_large = "font-size: 40px;"
        font_text_display = "font-size: 68px;"
        item_position = "background-color: transparent; color: #FFFFFF;font-size: 40px;"

        item_focused = "border-radius: 0px; border: 1px solid #696a6c; background-color: transparent;"
        # item_focused = f'''
        #     QLabel {{
        #         border-radius: 10px;
        #         border: 1px solid #181824;
        #         background-color: #FFFFFF; 
        #         color: #696a6c;
        #         font-size: 40px;"         
        #     }}'''
        # item_focused = "border-radius: 0px; border: 1px solid #696a6c; background-color: transparent;"
        # item_position = "border-radius: 0px; border: 1px solid transparent; background-color: transparent; color: #FFFFFF;font-size: 40px;"
        # item_position_color = "border-radius: 0px; border: 1px solid transparent; background-color: transparent; color: #696a6c;font-size: 40px;"
        camera_frame = "border-radius: 0px; border: 1px solid transparent; background-color: #2B2A3A; color: #FFFFFF;font-size: 40px;"
        camera_frame_focused = "border-radius: 0px; border: 1px solid #696a6c; background-color: #2B2A3A;color: #FFFFFF;font-size: 40px;"
        camera_frame_color = "border-radius: 0px; border: 1px solid #696a6c; background-color: #2B2A3A;color: #696a6c;font-size: 40px;"
        disable_button = "background-color: #A9A9A9; color: #FFFFFF; border-radius: 4px; padding: 5px;"
        enable_button = "background-color: #FFFFFF; color: #000000; border-radius: 4px; padding: 5px;"
        margin_50px = "margin: 50px;"
        # use stylesheet add_new_location hide text
        add_new_location = "border: 0; #4C4C4C; padding: 2px; border-radius: 10px"

        #Button Style
        button_positive = f'''
            QPushButton {{
                background-color: #696a6c;
                color: #EEEEEE;
                border-radius: 4px;
                padding: 4px 16px 4px 16px;
                
            }}
            QPushButton:hover {{
                    background-color: #3F3F87;
                    
                }}
            QPushButton:pressed {{
                    background-color: #3F3F87;
                    
                }}
            QPushButton:disabled {{
                    background-color: #8D9DB1;
                    
                }}
        '''
        button_negative = f'''
            QPushButton {{
                background-color: #656475;
                color: #E3E3E3;
                border-radius: 4px;
                padding: 4px 16px 4px 16px;
                
            }}
            QPushButton:hover {{
                    background-color: #5D5C6D;
                    
                }}
            QPushButton:pressed {{
                    background-color: #5D5C6D;
                    
                }}
            QPushButton:disabled {{
                    background-color: #656475;
                    
                }}
        '''

        button_ai_on = f'''
            QPushButton {{
                background-color: #696a6c;
                color: #EEEEEE;
                border-radius: 4px;
                padding: 4px 8px 4px 8px;
                
            }}
            QPushButton:hover {{
                    background-color: #A50F29;
                    color: #EEEEEE;
                    border-radius: 4px;
                    padding: 4px 8px 4px 8px;
                }}
            QPushButton:pressed {{
                    background-color: #A50F29;
                    
                }}
            QPushButton:disabled {{
                    background-color: #8D9DB1;
                    
                }}
        '''
        button_ai_off = f'''
            QPushButton {{
                background-color: #656475;
                color: #E3E3E3;
                border-radius: 4px;
                padding: 4px 8px 4px 8px;
                
            }}
            QPushButton:hover {{
                background-color: #656475;
                color: #E3E3E3;
                border-radius: 4px;
                padding: 4px 8px 4px 8px;
            }}
            QPushButton:pressed {{
                    background-color: #5D5C6D;
                    
                }}
            QPushButton:disabled {{
                    background-color: #656475;
                    
                }}
        '''

        button_number_ai = f"""
            QPushButton {{
                background-color: transparent;
                color: #E3E3E3;
                border: none;
            }}
            QPushButton:hover {{
                background-color: transparent;
                color: #E3E3E3;
                border: none;
                }}
            QPushButton:pressed {{
                background-color: transparent;
                color: #E3E3E3;
                border: none;
                }}
        """
        
        button_disable = f"""
            QPushButton {{
                background-color: #3A3A3A;
                color: #656475;
                border-radius: 4px;
                padding: 4px 16px 4px 16px;
                
            }}
            QPushButton:hover {{
                    background-color: #3A3A3A;
                    
                }}
            QPushButton:pressed {{
                    background-color: #3A3A3A;
                    
                }}
            QPushButton:disabled {{
                background-color: #3A3A3A;
                color: #656475;
                border-radius: 4px;
                padding: 4px 16px 4px 16px;  
                }}
        """

        button_on = f"""
                QPushButton {{
                    background-color: rgba(181, 18, 46, 1);
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    background-color: rgba(204, 80, 81, 1);
                    border-radius: 4px;
                }}
                """
        button_off = f"""
                QPushButton {{
                    background-color: rgba(69, 79, 103, 1);
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    background-color: rgba(204, 80, 81, 1);
                    border-radius: 4px;
                }}
                """
        button_toolbar_tab = f"""
                QPushButton {{
                    background-color: transparent;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    background-color: #656475;
                    border-radius: 4px;
                }}
                """
        button_style1 = f"""
                QPushButton {{
                    background-color: #696a6c;
                    color: #FFFFFF;
                    border-radius: 6px;
                    margin-right: 0px;
                    margin-left: 0px;
                    margin-top: 0px; 
                    font-size: 14px;
                    margin-bottom: 0px;
                }}
                QPushButton:hover {{
                    background-color: #CC5051;
                    margin-right: 0px;
                    margin-left: 0px;
                    margin-top: 0px;
                    margin-bottom: 0px;
                    font-size: 14px;
                }}
                QPushButton:pressed {{
                    background-color: #CC5051;
                    margin-right: 1px;
                    margin-left: 1px;
                    margin-top: 1px;
                    margin-bottom: 1px;
                    font-size: 14px;
                }}
                QPushButton:disabled {{
                    background-color: #8D9DB1;
                    margin-right: 1px;
                    margin-left: 1px;
                    margin-top: 1px;
                    margin-bottom: 1px;
                    font-size: 14px;
                }}
        """
        button_style2 = f"""
                QPushButton {{
                    background-color: rgba(141, 157, 177, 1); 
                    color: #FFFFFF;
                    border-radius: 6px;
                    margin-right: 0px;
                    margin-left: 0px;
                    margin-top: 0px;
                    margin-bottom: 0px;
                    font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: rgba(141, 157, 177, 0.8);
                    margin-right: 0px;
                    margin-left: 0px;
                    margin-top: 0px;
                    margin-bottom: 0px;
                    font-size: 14px;
                }}
                QPushButton:pressed {{
                    background-color: rgba(141, 157, 177, 0.8);
                    margin-right: 1px;
                    margin-left: 1px;
                    margin-top: 1px;
                    margin-bottom: 1px;
                    font-size: 14px;
                }}
                QPushButton:disabled {{
                    background-color: rgba(141, 157, 177, 0.8);
                    margin-right: 1px;
                    margin-left: 1px;
                    margin-top: 1px;
                    margin-bottom: 1px;
                    font-size: 14px;
                }}
        """
        button_style3 = f"""
                QPushButton {{
                    background-color: #34495E; color: #FFFFFF; border-radius: 6px; margin-right: 0px; margin-left: 0px;
                    margin-top: 0px; margin-bottom: 0px; font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: #2C3E50; margin-right: 0px; margin-left: 0px;
                    margin-top: 0px; margin-bottom: 0px; font-size: 14px;
                }}
                QPushButton:pressed {{
                    background-color: #2C3E50; margin-right: 1px; margin-left: 1px;
                    margin-top: 1px; margin-bottom: 1px; font-size: 14px;
                }}
                QPushButton:disabled {{
                    background-color: #8D9DB1; margin-right: 1px; margin-left: 1px;
                    margin-top: 1px; margin-bottom: 1px; font-size: 14px;
                }}
                """
        button_style4 = f"""
                QPushButton {{border: None; background-color: transparent;}}
                QPushButton:hover {{background-color: #2B2A3A;border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #2B2A3A;border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
                margin-bottom: 0.5px;}}
                """

        button_style5 = f"""
                QPushButton {{background-color: #2B2A3A;border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:hover {{background-color: #454F67;border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #454F67;border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
                margin-bottom: 0.5px;}}
                """

        button_style6 = f"""
                QPushButton {{background-color: rgba(255, 255, 255, 0.3); color: #FFFFFF;border-radius: 6px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:hover {{background-color: #CC5051;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #CC5051;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px;}}
                """
        button_style7 = f"""
                QPushButton {{background-color: #454F67; color: #FFFFFF;border-radius: 4px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:hover {{background-color: #5C687F;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #454F67;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px;}}
                """

        button_style8 = f"""
                QPushButton {{background-color: #696a6c; color: #FFFFFF;border-radius: 4px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:hover {{background-color: #696a6c;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #696a6c;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px;}}
                """
        button_style9 = f"""
                QPushButton {{background-color: #FFFFFF; color: #696a6c; border-radius: 6px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:hover {{background-color: #dfdfdf;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #dfdfdf;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px;}}
                """
        button_style10 = f"""
                QPushButton {{border: None; border-radius: 4px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:hover {{background-color: #5C687F;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #454F67;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px;}}
                """
        button_style11 = f"""
            QPushButton {{
                background-color: #696a6c;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }}
            QPushButton:hover {{
                background-color: #CC5051;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }}
            QPushButton:pressed {{
                background-color: #CC5051;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 16px;
                margin-bottom: 16px;
            }}
            QPushButton:disabled {{
                background-color: #8D9DB1;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 16px;
                margin-bottom: 16px;
            }}
            """
        button_style12 = f"""
            QPushButton {{
                background-color: #5C687F;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }}
            QPushButton:hover {{
                background-color: #5C687F;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }}
            QPushButton:pressed {{
                background-color: #5C687F;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 16px;
                margin-bottom: 16px;
            }}
            """
        button_style13 = f"""
            QPushButton {{
                background-color: rgba(141, 157, 177, 1);
                color: #FFFFFF;
                border: 2px solid rgba(141, 157, 177, 1);
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }}
            QPushButton:hover {{
                background-color: rgba(141, 157, 177, 0.8);
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }}
            QPushButton:pressed {{
                background-color: rgba(141, 157, 177, 0.8);
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 16px;
                margin-bottom: 16px;
            }}
            QPushButton:disabled {{
                background-color: rgba(141, 157, 177, 0.8);
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 16px;
                margin-bottom: 16px;
            }}
            """
        button_style_disable = f"""
            QPushButton {{background-color: #4C4C4C;color: #FFFFFF; border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
            margin-bottom: 0px;}}
            QPushButton:hover {{background-color: rgba(76, 76, 76, 0.3);color: #FFFFFF;border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
            margin-bottom: 0px;}}
            QPushButton:pressed {{background-color: rgba(76, 76, 76, 0.3);color: #FFFFFF;border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
            margin-bottom: 0.5px;}}
            """
        close_button_dialog_style = f"""
            QPushButton {{
                background-color: #656475; 
                border: None
                }}
            QPushButton:hover {{
                background-color: #656475;
                border-radius: 4px;
            }}
            QPushButton:pressed {{
                background-color: #656475;
                border-radius: 4px;
                }}
            """
        button_style14 = f"""
            QPushButton {{border: None}}
            QPushButton:hover {{background-color: rgba(255, 255, 255, 0.3);border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
            margin-bottom: 0px;}}
            QPushButton:pressed {{background-color: rgba(255, 255, 255, 0.3);border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
            margin-bottom: 0.5px;}}
            """
        button_style15 = f"""
            QPushButton {{background-color: #16BD1D;color: #FFFFFF; border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
            margin-bottom: 0px;}}
            QPushButton:hover {{background-color: rgba(22, 189, 29, 0.3);color: #FFFFFF;border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
            margin-bottom: 0px;}}
            QPushButton:pressed {{background-color: rgba(22, 189, 29, 0.3);color: #FFFFFF;border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
            margin-bottom: 0.5px;}}
            """
        button_style17 = f"""
                QPushButton {{border: None}}
                QPushButton:hover {{background-color: #efefef;border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #efefef;border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
                margin-bottom: 0.5px;}}
                """
        button_style18 = f"""
                QPushButton {{border: None}}
                QPushButton:hover {{background-color: #D2E3EE;border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: #D2E3EE;border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
                margin-bottom: 0.5px;}}
                """
        button_style19 = f"""
                QPushButton {{
                    background-color: rgba(0, 0, 0, 0.7);
                    border-radius: 4px;
                    padding: 5px 10px 5px 10px;
                    border: 1px solid transparent;
                }}
                QPushButton:hover {{
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 4px;
                    padding: 5px 10px 5px 10px;
                    border: 1px solid #FFFFFF;
                }}
                QPushButton:pressed {{
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 4px;
                    padding: 5px 10px 5px 10px;
                    border: 1px solid transparent;
                }}
                """
        button_style20 = f"""
            QPushButton {{
                background-color: #3388DC;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }}
            QPushButton:hover {{
                background-color: #3388DC;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 15px;
                margin-bottom: 15px;
            }}
            QPushButton:pressed {{
                background-color: #3388DC;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 16px;
                margin-bottom: 16px;
            }}
            QPushButton:disabled {{
                background-color: #3388DC;
                color: #FFFFFF;
                border-radius: 4px;
                
                margin-right: 15px;
                margin-top: 16px;
                margin-bottom: 16px;
            }}
            """
        button_style22 = f"""
            QPushButton {{
                background-color: #FFFFFF;
                color: #000000;
                border: 1px solid #696a6c;
                border-radius: 2px;
                
                padding: 4px 8px 4px 8px;
            }}
            QPushButton:hover {{
                background-color: #dfdfdf;
                color: #000000;
                border-radius: 2px;
                
                padding: 4px 8px 4px 8px;
            }}
            QPushButton:pressed {{
                background-color: #dfdfdf;
                color: #000000;
                border-radius: 2px;
                
                padding: 4px 8px 4px 8px;
            }}
            QPushButton:disabled {{
                background-color: #8D9DB1;
                color: #000000;
                border-radius: 2px;
                
                padding: 4px 8px 4px 8px;
            }}
            """
        
        # used
        style_button_add = f"""
            QPushButton {{
                background-color: #696a6c;
                color: #FFFFFF;
                border-radius: 4px;
                padding: 4px 16px 4px 16px;
                
            }}
            QPushButton:hover {{
                    background-color: #3F3F87;
                    
                }}
            QPushButton:pressed {{
                    background-color: #3F3F87;
                    
                }}
            QPushButton:disabled {{
                    background-color: #8D9DB1;
                    
                }}
            """
        button_style23 = f"""
                QPushButton {{background-color: #696a6c; color: #FFFFFF;border-radius: 6px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px; font-size: 14px;}}
                QPushButton:hover {{background-color: #CC5051;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px; font-size: 14px;}}
                QPushButton:pressed {{background-color: #CC5051;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px; font-size: 14px;}}
                """
        button_style24 = f"""
                QPushButton {{background-color: #FFFFFF; color: #696a6c;border: 2px solid #696a6c;border-radius: 6px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px; font-size: 14px;}}
                QPushButton:hover {{background-color: #dfdfdf;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px; font-size: 14px;}}
                QPushButton:pressed {{background-color: #dfdfdf;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px; font-size: 14px;}}
                """
        button_style25 = f"""
                QPushButton {{background-color: #696a6c; color: #FFFFFF;border-radius: 2px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px; font-size: 10px;}}
                QPushButton:hover {{background-color: #CC5051;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px; font-size: 10px;}}
                QPushButton:pressed {{background-color: #CC5051;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px; font-size: 10px;}}
                """
        button_style26 = f"""
                QPushButton {{background-color: #FFFFFF; color: #696a6c;border: 1px solid #696a6c;border-radius: 2px;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px; font-size: 10px;}}
                QPushButton:hover {{background-color: #dfdfdf;margin-right: 0px;margin-left: 0px;
                margin-top: 0px;
                margin-bottom: 0px; font-size: 10px;}}
                QPushButton:pressed {{background-color: #dfdfdf;margin-right: 1px;margin-left: 1px;
                margin-top: 1px;
                margin-bottom: 1px; font-size: 10px;}}
                """
        slider_style = """
                QSlider {
                    background-color: transparent;
                    }
                QSlider::groove:horizontal {
                    border: 1px solid;
                    height: 2px;
                    /*margin: 30px;*/
                    background-color: #656475;
                    /*position: absolute;*/
                    left: 5px; right: 5px;
                    }
                QSlider::handle:horizontal {
                    background-color: #696a6c;
                    height: 12px;
                    width: 12px;
                    border-radius: 5px;
                    margin: -5px -6px;
                    }
                QSlider::handle:horizontal:pressed {
                    background-color: #A8A8A8;
                }
                QSlider::add-page:horizontal {
                    background: #656475;
                }
                QSlider::sub-page:horizontal {
                    background: #696a6c;
                }
                """
        scrollbar_ver_style = f"""
                QScrollBar:vertical {{
                    background-color: transparent;
                    width: 4px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: #656475;
                    border-radius: 2px;
                    min-height: 8px;
                    margin-right: 1px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
            """
        scrollbar_hor_style = f'''
                QScrollBar::horizontal {{
                    background-color: transparent;
                    height: 8px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:horizontal {{
                    background-color: #656475;
                    border-radius: 2px;
                    min-width: 2px;
                }}
                QScrollBar::add-line:horizontal {{
                    background: none;
                }}
                QScrollBar::sub-line:horizontal {{
                    background: none;
                }}
                QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                    background: none;
                }}
                QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
        '''
        scrollbar_vertical_style1 = f"""
                QScrollBar:vertical {{
                    background-color: transparent;
                    width: 4px;
                    height: 20px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: #656475;
                    border-radius: 4px;
                    min-height: 10px;
                    height: 20px;
                    margin-right: 1px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
                """
        scrollbar_horizontal_style1 = f'''
                QScrollBar::horizontal {{
                    background-color: transparent;
                    height: 4px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:horizontal {{
                    background-color: #656475;
                    border-radius: 4px;
                    min-width: 1px;
                }}
                QScrollBar::add-line:horizontal {{
                    background: none;
                }}
                QScrollBar::sub-line:horizontal {{
                    background: none;
                }}
                QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                    background: none;
                }}
                QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
        '''
        line_edit_style = "background-color: #FFFFFF;color: #767676;border: None"
        line_edit_style1 = "background-color: #FFFFFF;color: #000000;border: None"
        label_style = '''
                QLabel {
                    font: bold;
                    color: #000000;
                    font-size: 14px;
                }'''
        label_style1 = '''
                QLabel {
                    background-color: transparent;
                    font: bold;
                    color: #FFFFFF;
                    font-size: 40px;
                }'''
        label_style2 = '''
                QLabel {
                    background-color: #808080;
                    font: bold;
                    color: #FFFFFF;
                    font-size: 60px;
                    border: 2px solid #FFFFFF;
                }'''
        label_style3 = '''
                QLabel {
                    background-color: transparent;
                    font: bold;
                    color: #696a6c;
                    font-size: 40px;
                }'''
        label_style4 = '''
                QLabel {
                    background-color: #808080;
                    font: bold;
                    color: #696a6c;
                    font-size: 60px;
                    border: 2px solid #FFFFFF;
                }'''
        progress_dialog = """
            QProgressDialog {
                background-color: #efefef;
                color: #000000;
                border: 2px solid #cccccc;                                          
            }
            QProgressDialog QLabel {
                color: #000000;                                        
            }

            QProgressBar {
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
            }

            QProgressBar::chunk {
                background-color: #696a6c;
                width: 10px;
            }
            """

        context_menu = f"""
            QMenu {{
                background-color: #656475;
                border-radius: 2px;
                border: 1px solid #656475;
            }}
            QMenu::item {{
                padding: 2px 20px 2px 2px;
                color: white;
                margin: 2px 2px 2px 2px;
                border-radius: 0px;
                border: 1px solid #656475;
                background-color: #656475;
            }}
            QMenu::item:selected {{
                background-color: #2E2E2E;
                color: #FFFFFF;
                border-radius: 2px;
                border: 1px solid #2E2E2E;
                
            }}
            QMenu::item:disabled {{
                color: #E3E3E3;
            }}
            QMenu::separator {{
                height: 1px;
                background:blue;
                margin-left:5px;
                margin-right:5px;"
            }}
            """
        dialog_style = f"""
            QDialog {{
                background-color: #efefef;
                color: #000000;
                border: 2px solid
            }}
            """


    class PrimaryStyleSheet:
        @staticmethod
        def get_button_positive(theme_instance: object) -> object:
            return f'''
            QPushButton {{
                background-color: {theme_instance.get_theme_attribute("Color", "primary")};
                color: {theme_instance.get_theme_attribute("Color", "main_background")};
                border-radius: 4px;
                padding: 4px 16px 4px 16px;
                
            }}
            QPushButton:hover {{
                background-color: {theme_instance.get_theme_attribute("Color", "primary", 0.8)};   
            }}
            QPushButton:pressed {{
                background-color: {theme_instance.get_theme_attribute("Color", "primary")}; 
            }}
            QPushButton:disabled {{
                background-color: {theme_instance.get_theme_attribute("Color", "primary", 0.5)};   
            }}
        '''
        
        @staticmethod
        def get_add_button_style(theme_instance: object) -> object:
            return f"""
            QPushButton {{
                background-color: {theme_instance.get_theme_attribute("Color", "primary")};
                color: {theme_instance.get_theme_attribute("Color", "main_background")};
                font-weight: bold;
                border-radius: 8px;
                padding: 4px 32px 4px 32px;
            }}
            QPushButton:hover {{
                background-color: {theme_instance.get_theme_attribute("Color", "primary", 0.8)};  
            }}
            """
        
        @staticmethod
        def get_context_menu_style(theme_instance: object) -> object:
            return f"""
                QMenu {{
                    background-color: {theme_instance.get_theme_attribute("Color", "main_background")};
                    border-radius: 10px;
                    border: 1px solid {theme_instance.get_theme_attribute("Color", "divider")};
                }}
                QMenu::item {{
                    padding: 2px 20px 2px 2px;
                    color: {theme_instance.get_theme_attribute("Color", "text_color_all_app")};
                    margin: 2px 2px 2px 2px;
                    background-color: transparent;
                    border-radius: 0px;
                    
                }}
                QMenu::item:selected {{
                    background-color: transparent;
                    color: {theme_instance.get_theme_attribute("Color", "primary")};
                    border-radius: 2px;
                    border: 1px solid {theme_instance.get_theme_attribute("Color", "primary")};

                }}
                QMenu::item:disabled {{
                    color: {theme_instance.get_theme_attribute("Color", "text_disable")};
                }}
                QMenu::separator {{
                    height: 1px;
                    margin-left:5px;
                    margin-right:5px;"
                }}
                """

        @staticmethod
        def get_tool_button_style(theme_instance: object, icon) -> object:
            return f"""
                QPushButton {{
                    background-color: transparent;
                    border-radius: 4px;
                    qproperty-icon: url({icon});
                }}
                QPushButton:hover {{
                    background-color: {theme_instance.get_theme_attribute("Color", "hover_button")};
                    border-radius: 4px;
                    
                }}
           """
        
        # dialog tabbar
        @staticmethod
        def get_dialog_tabbutton_active_style(theme_instance: object) -> object:
            # Dialog style
            return f'''
                QPushButton{{
                    background-color: transparent;
                    padding: 4px 16px 4px 16px;
                    color: {theme_instance.get_theme_attribute("Color", "text")};
                    border-bottom: 2px solid {theme_instance.get_theme_attribute("Color", "primary")};
                    border-top-left-radius: 4px;  /* Set top-left border radius */
                    border-top-right-radius: 4px;
                    font-size: 14px
                }}
            '''
        
        @staticmethod
        def get_dialog_tabbutton_inactive_style(theme_instance: object) -> object:
            # Dialog style
            return  f'''
                QPushButton{{
                    background-color: transparent;
                    padding: 4px 16px 4px 16px;
                    color: {theme_instance.get_theme_attribute("Color", "text")};
                    border: None;
                    font-size: 14px
                }}
            '''
        
        @staticmethod
        def get_dialog_header_style(theme_instance: object) -> object:
            return f'''
                QWidget#title_bar {{
                    background-color: {theme_instance.get_theme_attribute("Color", "dialog_header_background")};
                    border-bottom: 1px solid {theme_instance.get_theme_attribute("Color", "dialog_header_background")}
                }}
                QLabel{{
                    color: {Style.PrimaryColor.white_2}; 
                    font-size: {Style.Size.body_strong}px; 
                    font-weight: 600; 
                    background-color: {theme_instance.get_theme_attribute("Color", "dialog_header_background")};
                }}
                QPushButton {{
                    background-color: {theme_instance.get_theme_attribute("Color", "dialog_header_background")}; 
                    border: None
                }}
                
            '''
        
        @staticmethod
        def get_dialog_general_style(theme_instance: object) -> object:
            return f'''
                QDialog {{
                    background-color: {theme_instance.get_theme_attribute("Color", "dialog_body_background")};
                    color: {theme_instance.get_theme_attribute("Color", "dialog_text")};
                }}
                QWidget{{
                    background-color: {theme_instance.get_theme_attribute("Color", "dialog_body_background")};
                    color: {theme_instance.get_theme_attribute("Color", "dialog_text")};
                }}
                
            '''
        
        @staticmethod
        def get_dialog_footer_style(theme_instance: object) -> object:
            return f'''
                QWidget#footer_widget {{
                    background-color: {theme_instance.get_theme_attribute("Color", "dialog_body_background")};
                    border-top: 1px solid {theme_instance.get_theme_attribute("Color", "dialog_header_background")};
                }}

                QPushButton#negative {{
                    background-color: #656475;
                    color: #E3E3E3;
                    border-radius: 4px;
                    padding: 4px 16px 4px 16px;
                }}
                QPushButton#negative:hover {{
                        background-color: #5D5C6D;
                        
                    }}
                QPushButton#negative:pressed {{
                        background-color: #5D5C6D;
                        
                    }}
                QPushButton#negative:disabled {{
                        background-color: #656475;
                        
                    }}

                QPushButton#positive {{
                    background-color: #696a6c;
                    color: #EEEEEE;
                    border-radius: 4px;
                    padding: 4px 16px 4px 16px;
                    
                }}
                QPushButton#positive:hover {{
                        background-color: #3F3F87;
                        
                    }}
                QPushButton#positive:pressed {{
                        background-color: #3F3F87;
                        
                    }}
                QPushButton#positive:disabled {{
                        background-color: #8D9DB1;
                        
                    }}
            '''
        
        @staticmethod
        def get_custom_slider_style(theme_instance: object) -> object:
            return f'''
                QSlider {{
                    background-color: transparent;
                    }}
                QSlider::groove:horizontal {{
                    border: 1px solid;
                    height: 2px;
                    /*margin: 30px;*/
                    background-color: #656475;
                    /*position: absolute;*/
                    left: 5px; right: 5px;
                    }}
                QSlider::handle:horizontal {{
                    background-color: #696a6c;
                    height: 12px;
                    width: 12px;
                    border-radius: 5px;
                    margin: -5px -6px;
                    }}
                QSlider::handle:horizontal:pressed {{
                    background-color: #A8A8A8;
                }}
                QSlider::add-page:horizontal {{
                    background: #656475;
                }}
                QSlider::sub-page:horizontal {{
                    background: #696a6c;
                }}

                QLabel{{
                    color: {theme_instance.get_theme_attribute("Color", "dialog_text")};
                }}
            '''
        
        def get_button_style4(theme_instance: object) -> object: 
            return f"""
                QPushButton {{border: None; background-color: transparent;}}
                QPushButton:hover {{background-color: {theme_instance.get_theme_attribute("Color", "file_button_focus")};border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
                margin-bottom: 0px;}}
                QPushButton:pressed {{background-color: {theme_instance.get_theme_attribute("Color", "file_button_focus")};border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
                margin-bottom: 0.5px;}}
                """
        def get_camera_grid_item_normal_style(theme_instance: object) -> object:
            return f"""
                background-color: transparent; color: {theme_instance.get_theme_attribute("Color", "text_color_all_app")};font-size: 40px;
                """
        
        def get_camera_grid_item_hover_style(theme_instance: object) -> object:
            return f"""
            border-radius: 0px; background-color: {theme_instance.get_theme_attribute("Color", "camera_widget_background")};
            color: {theme_instance.get_theme_attribute("Color", "camera_widget_text_clicked")};
            font-size: 40px;"""

    class PrimaryImage:
        close_tab_white = ":src/assets/tab_icon/close_tab_white.svg"
        logo_login_mini = ":src/assets/login_screen/logo_login_light_mini.svg"
        ########
        cursor = ":src/assets/images/cursor_arrow.cur"
        cursor_busy = ":src/assets/images/cursor_busy.gif"
        cursor_horizontal_resize = ":src/assets/images/cursor_horizontal.cur"
        cursor_vertical_resize = ":src/assets/images/cursor_vertical.cur"
        cursor_top_left_corner = ":src/assets/images/cursor_tlbr.cur"
        cursor_top_right_corner = ":src/assets/images/cursor_bltr.cur"
        cursor_link = ":src/assets/images/cursor_link.cur"
        cursor_help = ":src/assets/images/cursor_help.cur"
        cursor_move = ":src/assets/images/cursor_move.cur"
        add = ":/src/assets/images/plus.svg"

        add_camera_address = ":/src/assets/images/add_camera_address.png"
        delete_icon = ":/src/assets/images/delete_icon.svg"
        delete_icon_disable = ":/src/assets/images/delete_icon_disable.svg"
        delete = ":/src/assets/images/delete.png"
        delete_camera_address = ":/src/assets/images/delete_camera_address.png"
        delete_ai_script = ":src/assets/images/delete_ai_script.svg"
        show_hide = ":/src/assets/images/show_hide.png"
        close_stream = ":/src/assets/images/close_stream.png"
        sucess_result = ":/src/assets/images/sucess_result.png"
        fail_result = ":/src/assets/images/fail_result.png"
        info_result = ":/src/assets/images/info_result.png"
        close_notify = ":/src/assets/images/close_notify.png"
        edit_virtual = ":/src/assets/images/edit_virtual.png"
        remove_all_virtual = ":/src/assets/images/remove_all_virtual.png"
        add_virtual = ":/src/assets/images/add_virtual.png"
        icon64 = ":/src/assets/images/icon_64.png"
        icon48 = ":/src/assets/images/icon_48.png"
        icon32 = ":/src/assets/images/icon_32.png"
        icon128 = ":/src/assets/images/icon_128.png"
        restart_path = ":/src/assets/images/restart.png"

        ic_camera_connecting = ":src/assets/state/camera_connecting.png"
        ic_camera_disconnect = ":src/assets/state/camera_disconnect.svg"
        pause_path = ":src/assets/images/camera_pause.png"

        server = ":/src/assets/images/server.png"

        fast_forward = ":/src/assets/images/fast_forward.png"
        fast_backward = ":/src/assets/images/fast_backward.png"
        play_video = ":/src/assets/images/play_video.png"
        stop_video = ":/src/assets/images/stop_video.png"
        pause = ":/src/assets/images/pause.png"
        play = ":/src/assets/images/play.png"
        speed_video = ":/src/assets/images/speed_video.png"
        sound_video = ":/src/assets/images/sound_video.png"
        mute_sound_video = ":/src/assets/images/mute_sound_video.png"
        minimize_video = ":/src/assets/images/minimize_video.png"
        fullscreen_video = ":/src/assets/images/fullscreen_video.png"

        import_file = ":/src/assets/images/import_file.png"
        export_file = ":src/assets/images/export_file.png"
        edit = ":src/assets/images/edit.png"
        double_right = ":src/assets/images/double_right.png"
        plus = ":src/common/widget/feature_controller/images/plus.svg"
        record = ":src/assets/images/record.png"
        edit_map = ":src/assets/map/edit_map.png"
        rotate_map = ":src/assets/map/rotate_map.png"
        zoomin_map = ":src/assets/map/zoomin_map.png"
        zoomout_map = ":src/assets/map/zoomout_map.png"
        icon_building = ":src/assets/map/icon_building.svg"
        building_on = ":src/assets/map/building_on.svg"
        icon_floor = ":src/assets/map/icon_floor.svg"
        setting = ":src/assets/images/setting.png"
        show = ":src/assets/images/show.png"
        expand_right = ":src/assets/images/expand_right.svg"
        expand_bottom = ":src/assets/images/expand_bottom.svg"
        audio = ":src/assets/images/audio.png"
        ptz = ":src/assets/images/ptz.png"
        playback = ":src/assets/images/playback.png"
        zoom_digital = ":src/assets/images/zoom_digital.png"
        stop_live = ":src/assets/images/stop_live.png"
        info = ":src/assets/images/info.png"

        vehicle_detection_on = ":src/assets/ai_icons/vehicle_detection_on.svg"
        vehicle_detection_ver2 = ":src/assets/ai_icons/vehicle_detection_ver2.svg"
        vehicle_detection_off = ":src/assets/ai_icons/vehicle_detection_off.svg"
        face_recognition_on = ":src/assets/ai_icons/face_recognition_on.svg"
        face_recognition_off = ":src/assets/ai_icons/face_recognition_off.svg"
        face_recognition_ver2 = ":src/assets/ai_icons/face_recognition_ver2.svg"
        crowd_detection_on = ":src/assets/ai_icons/crowd_detection_on.svg"
        crowd_detection_off = ":src/assets/ai_icons/crowd_detection_off.svg"
        access_control_on = ":src/assets/ai_icons/access_control_on.svg"
        access_control_off = ":src/assets/ai_icons/access_control_off.svg"

        ic_recognition_security = ":src/assets/ai_icons/ic_recognition_security.svg"
        ic_risk_identification = ":src/assets/ai_icons/ic_risk_identification.svg"

        apply = ":src/assets/images/apply.png"
        speed = ":src/assets/images/speed.png"
        share = ":src/assets/images/share.png"
        cut = ":src/assets/images/cut.png"
        recording_icon_header = ":src/assets/images/recording.png"

        icon_volume = ":src/assets/camera_stream/icon_volume.svg"
        # icon_ptz_off = ":src/assets/camera_stream/icon_ptz_off.svg"
        # expand_camera = ":src/assets/camera_stream/expand_camera.svg"
        # shrink_camera = ":src/assets/camera_stream/shrink_camera.svg"
        # icon_record = ":src/assets/camera_stream/icon_record.svg"
        # icon_close = ":src/assets/camera_stream/icon_close.svg"
        # icon_crop_off = ":src/assets/camera_stream/icon_crop_off.svg"
        # icon_drag_zoom_off = ":src/assets/camera_stream/icon_drag_zoom_off.svg"
        # ptz_arrow = ":src/assets/camera_stream/ptz_arrow.svg"
        icon_ptz_arrow_off = ":src/assets/camera_stream/icon_ptz_arrow_off.svg"

        icon_ptz_on = ":src/assets/camera_stream/icon_ptz_on.svg"
        icon_crop_on = ":src/assets/camera_stream/icon_crop_on.svg"
        icon_drag_zoom_on = ":src/assets/camera_stream/icon_drag_zoom_on.svg"
        icon_ptz_arrow_on = ":src/assets/camera_stream/icon_ptz_arrow_on.svg"

        lightning_on = ":src/assets/event/lightning_on.svg"
        lightning_off = ":src/assets/event/lightning_off.svg"
        alert_off = ":src/assets/event/alert_off.svg"
        alert_on = ":src/assets/event/alert_on.svg"
        calendar = ":src/assets/event/calendar.svg"
        playback_tab_select = ":src/assets/side_menu_icon/playback_screen_on.svg"
        device_tab_select = ":src/assets/side_menu_icon/device_screen_on.svg"
        map_tab_select = ":src/assets/side_menu_icon/map_screen_on.svg"
        user_tab_select = ":src/assets/side_menu_icon/user_screen_on.svg"
        setting_tab_select = ":src/assets/side_menu_icon/setting_screen_on.svg"
        playback_tab_not_select = ":src/assets/side_menu_icon/playback_screen_off.svg"
        map_tab_not_select = ":src/assets/side_menu_icon/map_screen_off.svg"
        logo_tab_bar = ":src/assets/side_menu_icon/logo_tab_bar.svg"

        mic_disable = ":src/assets/images/mic_disable.png"
        record_off = ":src/assets/images/record_off.png"
        record_disable = ":src/assets/images/record_disable.png"
        record_on = ":src/assets/images/record_on.png"
        capture_disable = ":src/assets/images/capture_disable.png"
        capture_off = ":src/assets/images/capture_off.png"
        fullscreen_off = ":src/assets/images/fullscreen_off.png"
        volume_disable = ":src/assets/images/volume_disable.png"
        volume_off = ":src/assets/images/volume_off.png"
        volume_on = ":src/assets/images/volume_on.png"
        window_size_off = ":src/assets/images/window_size_off.png"
        close_tab = ":src/assets/images/close_tab.png"
        colume_option = ":src/assets/images/colume_option.png"
        state_online = ":src/assets/state/state_online.svg"
        state_offline = ":src/assets/state/state_offline.svg"

        # tool icons
        checkbox_checked = ":src/assets/tool_icons/checkbox_checked.svg"
        checkbox_unchecked = ":src/assets/tool_icons/checkbox_unchecked.svg"
        checkbox_partially_checked = ":src/assets/tool_icons/checkbox_partially_checked.svg"
        checkbox_ver2_checked = ":src/assets/tool_icons/checkbox_ver2_checked.svg"
        checkbox_ver2_unchecked = ":src/assets/tool_icons/checkbox_ver2_unchecked.svg"
        checkbox_unchecked_white = ":src/assets/tool_icons/checkbox_unchecked_white.svg"
        trash = ":src/assets/tool_icons/trash.svg"
        trash_ver2 = ":src/assets/tool_icons/trash_ver2.svg"
        trash_mini = ":src/assets/tool_icons/mini_trash.svg"
        pen = ":src/assets/tool_icons/edit.svg"
        pen_ver2 = ":src/assets/tool_icons/pen_ver2.svg"
        icon_close_dialog = ":src/assets/tool_icons/icon_close_dialog.svg"
        close_dialog_primary = ":src/assets/tool_icons/close_dialog_primary.svg"

        # tree view icon, menu in treeview icon
        camera_active_icon_red = ":src/assets/treeview_and_menu_treeview/camera_active_icon_red.svg"
        camera_active_icon_green = ":src/assets/treeview_and_menu_treeview/camera_active_icon_green.svg"
        camera_active_icon_green_qml = "qrc:/src/assets/treeview_and_menu_treeview/camera_active_icon_green.svg"
        norec_pin = ":src/assets/treeview_and_menu_treeview/norec_pin.svg"
        rec_pin = ":src/assets/treeview_and_menu_treeview/rec_pin.svg"
        rec_unpin = ":src/assets/treeview_and_menu_treeview/rec_unpin.svg"
        norec_unpin = ":src/assets/treeview_and_menu_treeview/norec_unpin.svg"

        norec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
        connected_norec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/connected_norec_unpin.svg"
        rec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
        rec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
        disconnected_norec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/disconnected_norec_unpin.svg"
        disrec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/disrec_unpin.svg"

        camera_inactive_icon = ":src/assets/treeview_and_menu_treeview/camera_in_active_treeview.svg"
        choose_position_icon = ":src/assets/treeview_and_menu_treeview/choose_position_camera_treeview.png"
        exit_stream_treeview = ":src/assets/treeview_and_menu_treeview/exit_stream_treeview.png"
        group_camera_treeview = ":src/assets/treeview_and_menu_treeview/group_camera_treeview.svg"
        open_in_tab = ":src/assets/treeview_and_menu_treeview/open_in_tab_treeview.png"
        treeview_server = ":src/assets/treeview_and_menu_treeview/treeview_server.svg"
        list_devices = ":src/assets/treeview_and_menu_treeview/list_devices.svg"
        list_map = ":src/assets/treeview_and_menu_treeview/list_map.svg"
        list_virtual_window = ":src/assets/treeview_and_menu_treeview/list_virtual_windows.svg"
        list_save_view = ":src/assets/treeview_and_menu_treeview/list_save_view.svg"
        close_all_virtual = ":/src/assets/treeview_and_menu_treeview/close_all_virtual.svg"
        open_all_virtual = ":/src/assets/treeview_and_menu_treeview/open_all_virtual.svg"
        previous_first = ":src/assets/images/first_page.svg"
        previous = ":src/assets/images/previous_page.svg"
        next = ":src/assets/images/next_page.svg"
        next_last = ":src/assets/images/latest_page.svg"
        expand_item_treeview = ":src/assets/treeview_and_menu_treeview/expand_treeview.svg"
        collapse_item_treeview = ":src/assets/treeview_and_menu_treeview/collapse_treeview.svg"
        Search = ":src/assets/treeview_and_menu_treeview/search_loop.svg"
        new_search = ":src/assets/treeview_and_menu_treeview/new_search.svg"
        Filter = ":src/assets/treeview_and_menu_treeview/Filter.png"
        Change_mode = ":src/assets/treeview_and_menu_treeview/change_mode.svg"
        ai_menu_filter = ":src/assets/treeview_and_menu_treeview/ai_menu_filter.png"
        complex_tree = ":src/assets/treeview_and_menu_treeview/complex_tree.png"
        state_menu_filter = ":src/assets/treeview_and_menu_treeview/state_menu_filter.png"

        # PTZ icon
        # left_top = ":src/assets/ptz_icon/left_top.svg"
        # top = ":src/assets/ptz_icon/top.svg"
        # right_top = ":src/assets/ptz_icon/right_top.svg"
        # left = ":src/assets/ptz_icon/left.svg"
        # around = ":src/assets/ptz_icon/around.svg"
        # right = ":src/assets/ptz_icon/right.svg"
        # left_bottom = ":src/assets/ptz_icon/left_bottom.svg"
        # bottom = ":src/assets/ptz_icon/bottom.svg"
        # right_bottom = ":src/assets/ptz_icon/right_bottom.svg"
        # zoom_in = ":src/assets/ptz_icon/zoom_in.svg"
        # focus_near = ":src/assets/ptz_icon/focus_near.svg"
        # iris_add = ":src/assets/ptz_icon/iris_plus.svg"
        # zoom_out = ":src/assets/ptz_icon/zoom_out.svg"
        # focus_far = ":src/assets/ptz_icon/focus_far.svg"
        # iris_not_add = ":src/assets/ptz_icon/iris_minus.svg"
        # drop_dow = ":src/assets/ptz_icon/dropdown.svg"
        # drop_dow_right = ":src/assets/ptz_icon/drop_down_right.svg"

        close_dialog = ":src/assets/images/close_dialog.png"
        # Login screen
        frame_login = ":src/assets/login_screen/frame_login.png"
        login_background = ":src/assets/login_screen/login_background.png"
        logo_login = ":src/assets/login_screen/logo_login.svg"
        add_server = ":src/assets/login_screen/add_server.svg"
        search_server = ":src/assets/login_screen/search_server.svg"
        edit_server = ":src/assets/login_screen/edit_server.png"
        user = ":src/assets/login_screen/user.png"
        lock = ":src/assets/login_screen/lock.png"
        eye_close = ":src/assets/login_screen/eye_close.png"
        eye = ":src/assets/login_screen/eye.png"
        # preset --------------
        add_preset = ":src/assets/preset_patrol/add_preset.svg"
        play_patrol_hover = ":src/assets/preset_patrol/play_patrol_hover.svg"
        stop_patrol_hover = ":src/assets/preset_patrol/stop_patrol_hover.svg"
        delete_preset_patrol = ":src/assets/preset_patrol/delete_preset_patrol.svg"
        down_preset = ":src/assets/preset_patrol/down_preset.svg"
        up_preset = ":src/assets/preset_patrol/up_preset.svg"
        call_preset_hover = ":src/assets/preset_patrol/call_preset_hover.svg"
        setting_preset_hover = ":src/assets/preset_patrol/setting_preset_hover.svg"
        delete_preset_hover = ":src/assets/preset_patrol/delete_preset_hover.svg"

        # call_preset = ":src/assets/preset_patrol/call_preset.svg"
        # setting_preset = ":src/assets/preset_patrol/setting_preset.svg"
        # delete_preset = ":src/assets/preset_patrol/delete_preset.svg"
        # play_patrol = ":src/assets/preset_patrol/play_patrol.svg"
        # stop_patrol = ":src/assets/preset_patrol/stop_patrol.svg"
        # preset = ":src/assets/preset_patrol/preset.svg"
        # patrol = ":src/assets/preset_patrol/patrol.svg"
        # pattern = ":src/assets/preset_patrol/pattern.svg"
        # ptz_advance_brightness = ":src/assets/preset_patrol/ptz_advance_brightness.svg"
        # ptz_advance_contrast = ":src/assets/preset_patrol/ptz_advance_contrast.svg"
        # ptz_advance_sharpness = ":src/assets/preset_patrol/ptz_advance_sharpness.svg"
        # ptz_advance_saturation = ":src/assets/preset_patrol/ptz_advance_saturation.svg"
        # ptz_advance_menu = ":src/assets/preset_patrol/ptz_advance_menu.svg"

        # down_speed = ":src/assets/ptz_icon/down_speed.svg"
        # up_speed = ":src/assets/ptz_icon/up_speed.svg"
        # tab
        icon_active = ":src/assets/tab_icon/icon_active.png"
        exit_fullscreen = ":src/assets/images/exit_fullscreen.png"
        exit_fullscreen1 = ":src/assets/images/exit_fullscreen1.png"
        # draw detect area
        draw_pencil = ":src/assets/images/draw_pencil.png"
        eraser = ":src/assets/images/eraser.png"
        draw_line_option = ":src/assets/images/line_option.svg"
        draw_polygon_option = ":src/assets/images/polygon_option.svg"
        down_arrow_custom = ":src/assets/images/down_arrow_custom.png"
        # right bar: filter event, time filter
        ic_filter = ":src/assets/event/filter.svg"
        refresh = ":src/assets/event/refresh.svg"
        ic_time_filter = ":src/assets/images/time_filter.png"
        play_alarm_sound = ":src/assets/images/play_sound.svg"
        disable_play_alarm_sound = ":src/assets/images/disable_play_alarm_sound.svg"
        warning = ":src/assets/images/warning.gif"
        close_alert = ":src/assets/images/close_alert.png"
        alert_event = ":src/assets/images/alert_event.png"
        state_read_warning = ":src/assets/images/state_read.svg"
        # Playback screen
        download_playback = ":src/assets/playback/download_playback.png"
        mute_fill_playback = ":src/assets/playback/mute_fill.png"
        next_fill_playback = ":src/assets/playback/next_fill.png"
        play_fill_playback = ":src/assets/playback/play_fill.png"
        previous_fill_playback = ":src/assets/playback/previous_fill.png"
        volume_up_playback = ":src/assets/playback/volume_up.png"
        pause_fill_playback = ":src/assets/playback/pause_fill_playback.png"
        
        play_playback = ":src/assets/playback/play.png"
        pause_playback = ":src/assets/playback/pause.png"
        next_frame_playback = ":src/assets/playback/next_frame.png"
        next_chunk_playback = ":src/assets/playback/next_chunk.png"
        previous_frame_playback = ":src/assets/playback/previous_frame.png"
        previous_chunk_playback = ":src/assets/playback/previous_chunk.png"
        zoom_in_playback = ":src/assets/playback/zoom_in.png"
        zoom_out_playback = ":src/assets/playback/zoom_out.png"
        calendar_playback = ":src/assets/playback/calendar_playback.png"
        setting_playback = ":src/assets/playback/setting_playback.png"
        volume_playback = ":src/assets/playback/volume_playback.png"
        thumbnails_playback = ":src/assets/playback/thumbnails_playback.png"

        edit_zones = ":src/assets/images/edit_zones.png"
        building_no_location = ":src/assets/treeview_and_menu_treeview/complex_tree.png"
        building_with_location = ":src/assets/treeview_and_menu_treeview/complex_tree.png"
        delete_camera_on_map = ":src/assets/images/delete_camera_on_map.png"
        radio_button_checked = ":src/assets/images/radio_button_checked.png"
        radio_button_uncheck = ":src/assets/images/radio_button_uncheck.png"
        ic_ptz = ":src/assets/images/ic_ptz.png"
        ic_dome = ":src/assets/images/ic_dome.png"
        ic_bullet = ":src/assets/images/ic_bullet.png"
        ic_ptz_disable = ":src/assets/images/ic_ptz_disable.png"
        ic_dome_disable = ":src/assets/images/ic_dome_disable.png"
        ic_bullet_disable = ":src/assets/images/ic_bullet_disable.png"
        ic_image_not_found = ":src/assets/images/image_not_found.png"
        # Custom grid
        caret_right_grid = ":src/assets/grid/caret_right_grid.svg"

        edit_map_name = ":src/assets/images/edit_map_name.png"
        edit_map_name_enable = ":src/assets/images/edit_map_name_enable.png"
        edit_map_camera = ":src/assets/images/edit_map_camera.png"
        edit_map_camera_enable = ":src/assets/images/edit_map_camera_enable.png"
        add_tab = ":src/assets/images/add_tab.png"
        ahihi = ":src/assets/images/ahihi.jpg"
        face = ":src/assets/images/face.jpg"
        ic_bullet_svg = ":src/assets/images/ic_bullet.svg"
        ic_dome_svg = ":src/assets/images/ic_dome.svg"
        ic_ptz_svg = ":src/assets/images/ic_ptz.svg"
        # Side Menu active icon
        icon_sidebar_big_left = ":src/assets/side_menu_icon/chevron_big_left.svg"
        icon_status_sidebar = ":src/assets/side_menu_icon/icon_status_sidebar.svg"
        icon_sidebar_big_right = ":src/assets/side_menu_icon/chevron_big_right.svg"
        icon_status_timeline = ":src/assets/side_menu_icon/icon_status_timeline.svg"
        icon_timeline_big_top = ":src/assets/side_menu_icon/chevron_big_top.svg"
        icon_timeline_big_bottom = ":src/assets/side_menu_icon/chevron_big_bottom.svg"
        # title bar
        close_application_white = ":src/assets/title_bar/close_window_white.svg"
        minimize_window_white = ":src/assets/title_bar/minimize_window_white.svg"
        maximize_window_white = ":src/assets/title_bar/maximize_window_white.svg"
        settings_application_white = ":src/assets/title_bar/settings_white.svg"
        normal_window_white = ":/src/assets/title_bar/normal_window_white.svg"

        ic_zone_activate = ":/src/assets/images/ic_zone_activate.png"
        edit_script = ":/src/assets/images/edit_script.png"
        alarm_alert_icon = ":/src/assets/images/alarm_alert.svg"
        camera_background_image = ":/src/assets/images/camera_background_image.svg"
        down_arrow_linedit = ":src/assets/arrows/down_arrow_lineedit.svg"
        down_arrow_spinbox = ":src/assets/arrows/down_arrow_spinbox.svg"
        up_arrow_spinbox = ":src/assets/arrows/up_arrow_spinbox.svg"
        arrow_combobox = ":src/assets/arrows/arrow_combobox.svg"
        no_data_image = ":src/assets/images/no_data_image.svg"
        # Table
        eye_in_table = ":/src/assets/images/eye_in_table.svg"
        edit_in_table = ":/src/assets/images/edit_in_table.svg"
        # Dialog
        add_avatar = ":/src/assets/images/add_avatar.svg"
        clear_input = ":src/assets/images/clear_input.svg"
        loading_image = ":src/assets/images/loading_image.png"

    class Toolstip:
        Server_Management = "Server_Management"
        Streaming = "Streaming"
        Playback = "Playback"
        Device_Management = "Device Management"
        Map = "Map"
        User_Management = "User Management"
        Setting = "Setting"
        User = "User"

    class Text:
        app_version = "Phiên bản 1.0.0"
        app_name = "VMS"
        add = "Thêm"
        delete = "Xóa"
        modified = "Sửa"
        camera_name = "Tên Camera"
        camera_location = "Vị trí"
        camera_ai_type = "Loại AI"
        no = "Không"
        yes = "Có"
        face = "Khuôn mặt"
        license_plate = "Biển số xe"
        use_detection = "Sử dụng nhận diện"
        are_u_sure_delete = "Are you sure you want to delete?"
        are_u_sure_replace = "This screen already contains a virtual window named 'ahihi'. Do you want to replace this virtual window"
        error = "Lỗi"
        error_message = "Có lỗi xảy ra, vui lòng thử lại sau"
        success = "Thành công"
        success_message = "Thao tác thành công"
        warning = "Cảnh báo"
        need_full_information = "Vui lòng nhập đầy đủ thông tin"
        select_camera_modified = "Chọn Camera cần sửa"
        add_camera = "Thêm mới Camera"
        modified_camera = "Sửa Camera"
        input_rtsp = "Nhập địa chỉ RTSP"
        input_information_camera = "Nhập thông tin Camera"
        delete_camera = "Xoá Camera"
        delete_group = "Xoá Group"
        rtsp = "RTSP"
        ip_camera = "IP Camera"
        ip_link = "Địa chỉ IP"
        port = "Cổng"
        account = "Tài khoản"
        password = "Mật khẩu"
        playback = "Xem lại"
        preview_camera = "Hiển thị Camera"
        device_management = "Quản lý Thiết bị"
        account_management = "Quản lý Tài khoản"
        setting = "Cài đặt"
        online = "Online"
        offline = "Offline"
        location_name = "Tên vị trí"
        add_new_location = "Thêm vị trí mới"
        open_map = "Mở bản đồ"
        save = "Lưu"
        cancel = "Cancel"
        create = "Create"
        state = "Trạng thái"
        location_cordination = "Tọa độ vị trí"
        location_name_exist = "Tên vị trí đã tồn tại"
        lost_connection = "Mất kết nối với server nên tạm thời đã lưu vị trí vào local thành công"
        location_coordinate_exist = "Tọa độ vị trí đã tồn tại"
        status = "Trạng thái"
        location_name_or_location_cordination_empty = (
            "Tên vị trí hoặc tọa độ vị trí không được để trống"
        )
        display = "Hiển thị"
        not_display = "Không hiển thị"
        notify = "Thông báo"
        not_found_camera = "Không tìm thấy Camera"
        searching_camera = "Đang tìm kiếm Camera"
        please_wait = "Vui lòng đợi"
        enable_camera_optimize = "Bật tối ưu Camera"

    class Notify:
        add_rtsp_success = "Thêm Camera thành công bằng RTSP"
        add_onvif_success = "Thêm Camera thành công bằng Onvif"
        import_file_success = "Thêm thành công danh sách camera"
        add_local_success = "Thêm Camera vào Nội bộ thành công"
        add_server_error = "Thêm Camera lên Máy chủ thất bại"
        add_local_error = "Thêm Camera vào Nội bộ thất bại"
        update_server_success = "Cập nhật Camera lên Máy chủ thành công"
        update_local_success = "Cập nhật Camera vào Nội bộ thành công"
        update_server_error = "Cập nhật Camera lên Máy chủ thất bại"
        update_local_error = "Cập nhật Camera vào Nội bộ thất bại"
        delete_server_success = "Xóa Camera trên Máy chủ thành công"
        delete_local_success = "Xóa Camera trong Nội bộ thành công"
        delete_server_error = "Xóa Camera trên Máy chủ thất bại"
        delete_local_error = "Xóa Camera trong Nội bộ thất bại"
        update_error = "Cap nhat that bai"
        add_group_success = "Thêm Group lên máy chủ thành công"
        update_group_success = "Cập nhật Group lên máy chủ thành công"
        delete_group_success = "Xóa Group trên máy chủ thành công"
        connect_onvif_fail = "Địa chỉ IP không hỗ trợ ONVIF hoặc sai tên tài khoản, mật khẩu. Vui lòng kiểm tra lại hoặc chọn hãng Camera"
        rtsp_fail = "Vui lòng nhập link RTSP"
        input_infor_fail = "Vui lòng nhập đủ thông tin"
        not_camera_selected = "Không có Camera nào được chọn."
        not_found_camera = "Không tìm thấy Camera nào"
        input_method_fail = "Vui lòng lựa chọn phương thức thêm Camera"
        ping_ip_fail = "Không kết nối được IP này."
        select_camera = "Vui lòng chọn Camera."
        ptz_not_supported = "Camera này không hỗ trợ PTZ"
        not_ai_restream = "This AI flows don't support AI stream"
        update_warning_alert_success = " Cập nhật cài đặt thành công"
        save_file_success = "Đã tải tập tin  thành công"
        save_file_fail = "Tải tập tin thất bại"
        add_virtual_window_fail = "Màn hình ảo này đã tồn tại"
        drag_to_zoom_not_supported = "Camera này không hỗ trợ Drag to Zoom "

    class Settings:
        server = "Server"
        config_server = "Cài đặt Server"
        delete_data = "Xóa dữ liệu"
        delete_local_data = "Xóa dữ liệu nội bộ"
        address_server = "Địa chỉ Server: "
        btn_change_server = "Thay đổi"
        btn_delete = "Xóa"
        option = "Cài đặt khác"

    class RATIO:
        RATIO_16_9 = 16 / 9
        RATIO_4_3 = 4 / 3
        RATIO_1_1 = 1

    class Menu:
        stop = "src/assets/menu_icon/StopCircle.png"
        camera = "src/assets/menu_icon/Camera.png"
        rec = "src/assets/menu_icon/REC.png"
        ptz_control2 = "src/assets/menu_icon/PTZcontrol2.png"
        flag = "src/assets/menu_icon/Flag.png"
        playback = "src/assets/menu_icon/Playback.png"
        volume = "src/assets/menu_icon/VolumeUp.png"
        slider = "src/assets/menu_icon/Sliders.png"
        mic = "src/assets/menu_icon/Mic.png"
        ic_full_screen = "src/assets/menu_icon/ic_full_screen.png"
        close = "src/assets/menu_icon/Close.png"

    class Actions:
        stop_live = "Stop Live View"
        capture = "Capture"
        start_recording = "Start Recording"
        ptz_control = "Open PTZ control"
        press_patrol = "Open Press & Patrol"
        switch_playback = "Switch to instant playback"
        enable_audio = "Enable Audio"
        disable_audio = "Disable Audio"
        image_adj = "Open Image Adjustment"
        microphone = "Turn on Microphone"
        full_screen = "Full Screen"
        exit_streaming = "Exit Streaming"
        standard = "Standard"
        advance = "Advance"

    class Language:
        vie = ":/src/languages/qt_vi_VN.qm"
        en = ":/src/languages/qt_en_US.qm"
        ru = ":/src/languages/qt_ru_RU.qm"


    class Audio:
        notify_sound = "src/assets/audio/notifi.mp3"

    class QMLLink:
        schedule_link = "src/common/qml/videoplayback/ScheduleUI.qml"
