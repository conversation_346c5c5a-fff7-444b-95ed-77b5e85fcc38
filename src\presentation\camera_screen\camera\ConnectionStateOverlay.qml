import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

/**
 * ConnectionStateOverlay.qml - Reusable component cho connection state display
 *
 * CHỨC NĂNG CHÍNH:
 * - Hi<PERSON>n thị trạng thái kết nối camera (connecting, disconnected, connected)
 * - Layout tối ưu với ColumnLayout
 * - Animation cho loading state
 * - Responsive design theo kích thước parent
 * - Single source of truth cho connection state
 *
 * KIẾN TRÚC:
 * - Centralized state management
 * - Optimized layout system
 * - Dynamic z-index từ model
 * - Reusable component pattern
 */
Item {
    id: root

    // Required properties
    property var itemData: null
    property bool isDarkTheme: true
    property int baseZIndex: 20


    // Component visibility - hiển thị khi có logo hoặc text
    visible: {
        if (root.itemData) {
            var state = root.itemData.connectionState
            var isVisible = state !== "started" // connecting, buffering, disconnected, stopped
            console.log("🎭 [STATE_FLOW] ConnectionStateOverlay: Camera",
                       root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown",
                       "connectionState =", state,
                       "→ visible =", isVisible)
            return isVisible
        }
        console.log("⚠️ [STATE_FLOW] ConnectionStateOverlay: No itemData, visible = false")
        return false
    }

    // Dynamic sizing based on parent
    anchors.centerIn: parent
    width: Math.max(120, Math.min(200, parent.width * 0.6))
    height: Math.max(80, Math.min(120, parent.height * 0.4))
    z: baseZIndex

    // // Background overlay cho better visibility
    // Rectangle {
    //     id: backgroundOverlay
    //     anchors.fill: parent
    //     color: root.isDarkTheme ? "#80000000" : "#80ffffff"
    //     radius: 8
    //     opacity: 0.8

    //     // Subtle border cho better definition
    //     border.width: 1
    //     border.color: root.isDarkTheme ? "#40ffffff" : "#40000000"
    // }

    // Main content layout
    ColumnLayout {
        id: contentLayout
        anchors.centerIn: parent
        spacing: 8
        width: parent.width - 16  // Padding

        // Row chứa icon và text trạng thái
        RowLayout {
            id: rowStatus
            Layout.alignment: Qt.AlignHCenter
            Layout.fillWidth: true
            spacing: 8

            // Hiệu ứng loading 3 bước khi connecting
            Item {
                id: loadingIcons
                width: Math.max(20, Math.min(32, root.width / 8))
                height: width
                visible: root.itemData && root.itemData.connectionState === "connecting"

                property int currentStep: 1

                Timer {
                    id: loadingTimer
                    interval: 300
                    running: loadingIcons.visible
                    repeat: true
                    onTriggered: {
                        loadingIcons.currentStep = loadingIcons.currentStep % 3 + 1
                    }
                }

                // Loading1
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading1.svg"
                    opacity: loadingIcons.currentStep === 1 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
                // Loading2
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading2.svg"
                    opacity: loadingIcons.currentStep === 2 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
                // Loading3
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading3.svg"
                    opacity: loadingIcons.currentStep === 3 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
            }

            // Logo hiển thị khi stopped
            Image {
                id: logoImage
                // Smaller fixed size: 48px max, scale down for small containers
                width: Math.max(100, Math.min(24, root.width * 0.15))
                height: width
                fillMode: Image.PreserveAspectFit
                visible: {
                    var shouldShow = root.itemData && root.itemData.connectionState === "stopped"
                    if (shouldShow) {
                        console.log("🖼️ [STATE_FLOW] ConnectionStateOverlay: Logo VISIBLE for camera",
                                   root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown",
                                   "state =", root.itemData.connectionState)
                    }
                    return shouldShow
                }
                source: "qrc:/src/assets/login_screen/logo.svg"

                // Center trong RowLayout
                Layout.alignment: Qt.AlignHCenter
                Layout.preferredWidth: width
                Layout.preferredHeight: height
            }

            // Status text
            Text {
                id: statusText
                Layout.alignment: Qt.AlignVCenter
                text: {
                    if (root.itemData) {
                        var displayText = ""
                        if (root.itemData.connectionState === "connecting") {
                            displayText = qsTr("Connecting")
                        } else if (root.itemData.connectionState === "buffering") {
                            displayText = root.itemData.percent
                        }
                        // Không hiển thị text cho trạng thái stopped - chỉ hiển thị logo

                        console.log("📝 [STATE_FLOW] ConnectionStateOverlay: Camera",
                                   root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown",
                                   "state =", root.itemData.connectionState,
                                   "→ text =", displayText)
                        return displayText
                    }
                    return ""
                }
                font.pixelSize: Math.max(12, Math.min(16, root.width / 12))
                font.bold: true
                color: {
                    var textColor = "white"
                    if (root.itemData) {
                        if (root.itemData.connectionState === "connecting") {
                            textColor = "#F6BE00"
                        } else if (root.itemData.connectionState === "buffering") {
                            textColor = "#F6BE00"
                        }
                        // Không cần color cho stopped vì không hiển thị text

                        console.log("🎨 [STATE_FLOW] ConnectionStateOverlay: Camera",
                                   root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown",
                                   "state =", root.itemData.connectionState,
                                   "→ color =", textColor)
                    }
                    return textColor
                }
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                wrapMode: Text.WordWrap
                maximumLineCount: 2
                elide: Text.ElideRight
                opacity: text !== "" ? 1.0 : 0.0
                Behavior on opacity {
                    NumberAnimation { duration: 200 }
                }
            }
        }
    }

    // Component lifecycle
    Component.onCompleted: {
        console.log("🎬 [STATE_FLOW] ConnectionStateOverlay: Component created for camera",
                   root.itemData && root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown")
    }

    Component.onDestruction: {
        console.log("🗑️ [STATE_FLOW] ConnectionStateOverlay: Component destroyed for camera",
                   root.itemData && root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown")
    }
}
