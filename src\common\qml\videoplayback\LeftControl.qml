import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 2.15
import models 1.0
Rectangle {
    // anchors.fill: parent
    width: parent.width
    height: parent.height
    property string previous_chunk: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_image_theme_by_key("previous_chunk") : ""
    // property string previous_frame: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_image_theme_by_key("previous_frame") : ""
    property string new_pause: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_image_theme_by_key("new_pause") : ""
    property string new_play: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_image_theme_by_key("new_play") : ""
    property string next_chunk_icon: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_image_theme_by_key("next_chunk") : ""
    // property string next_frame: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_image_theme_by_key("next_frame") : ""
    property color backgroundColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("main_background") : "#F5F5F5"
    property color textColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("text_color_all_app") : "#F5F5F5"
    property bool isProcessing: false
    color: backgroundColor

    Connections{
        target: timeLineManager.timeLineController
        function onThemeChanged() {
            previous_chunk = timeLineManager.timeLineController.get_image_theme_by_key("previous_chunk")
            // previous_frame = timeLineManager.timeLineController.get_image_theme_by_key("previous_frame")
            new_pause = timeLineManager.timeLineController.get_image_theme_by_key("new_pause")
            new_play = timeLineManager.timeLineController.get_image_theme_by_key("new_play")
            next_chunk_icon = timeLineManager.timeLineController.get_image_theme_by_key("next_chunk")
            // next_frame = timeLineManager.timeLineController.get_image_theme_by_key("next_frame")
            backgroundColor = timeLineManager.timeLineController.get_color_theme_by_key("main_background")
            textColor = timeLineManager.timeLineController.get_color_theme_by_key("text_color_all_app")
            color = backgroundColor
        }

    }
    Timer {
        id: antiSpamTimer
        interval: 500
        running: false
        repeat: false
        onTriggered: isProcessing = false
        }
    ColumnLayout {
        // anchors.centerIn: parent.centerIn
        anchors.centerIn: parent
        spacing: 5  // Khoảng cách giữa các layout
        Text
        {
            id: clockLabel
            height: 28
            color: textColor
            font.pixelSize: 16
            font.weight: Font.DemiBold
            // verticalAlignment: Text.AlignVCenter
            Layout.alignment: Qt.AlignHCenter 

            Timer
            {
                interval: 1000
                repeat: true
                triggeredOnStart: true
                running: timeLineManager.timeLineController.showClock

                onTriggered:
                {
                    clockLabel.text = new Date().toLocaleTimeString(Qt.locale(), "h:mm:ss A");
                }
            }
        }
        RowLayout {
            id: rowTop
            Layout.preferredHeight:30
            Layout.preferredWidth:180
            Layout.leftMargin: 5
            Layout.rightMargin: 5
            spacing: 5  // Khoảng cách giữa các nút

            IconButton {
                // height: 30
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: previous_chunk
                isDisabled: isProcessing ? true 
                            : timeLineManager.timeLineController.isPreviousChunk === true ? false 
                            : true
                onClicked:  {
                    isProcessing = true
                    timeLineManager.timeLineController.isPreviousChunkClicked()
                    antiSpamTimer.start()
                }

            }
            // IconButton {
            //     // height: 30
            //     Layout.fillWidth: true
            //     Layout.fillHeight: true
            //     icon.source: previous_frame
            //     isDisabled: timeLineManager.timeLineController.isLive === true ? true : false
            //     onClicked:  {
            //         timeLineManager.timeLineController.speedStatusChanged(-1)
            //     }
            // }
            IconButton {
                // height: 30
                Layout.fillWidth: true
                Layout.fillHeight: true
                isDisabled: isProcessing
                icon.source: timeLineManager.timeLineController.isPlay ? new_pause 
                            : new_play
                onClicked:  {
                    isProcessing = true
                    timeLineManager.timeLineController.isPlay = !timeLineManager.timeLineController.isPlay
                    antiSpamTimer.start()
                }
            }
            // IconButton {
            //     // height: 30
            //     Layout.fillWidth: true
            //     Layout.fillHeight: true
            //     icon.source: next_frame
            //     isDisabled: timeLineManager.timeLineController.isLive === true ? true : false
            //     onClicked:  {
            //         timeLineManager.timeLineController.speedStatusChanged(1)
                    
            //     }
            // }
            IconButton {
                id: next_chunk
                // height: 30
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: next_chunk_icon
                isDisabled: isProcessing ? true 
                            : timeLineManager.timeLineController.isNextChunk === true ? false 
                            : true 
                onClicked:  {
                    isProcessing = true
                    timeLineManager.timeLineController.isNextChunkClicked()
                    antiSpamTimer.start()
                    }
                }

        }
        RowLayout {
            // anchors.top: rowTop.bottom
            Layout.preferredHeight:30
            Layout.preferredWidth:190
            Layout.leftMargin: 5
            Layout.rightMargin: 5
            spacing: 5  // Khoảng cách giữa các nút
            // Layout.alignment: Qt.AlignHCenter

            ControlButton {
                text: "1X"
                Layout.fillWidth: true
                Layout.fillHeight: true
                enabled: !isProcessing
                buttonType: timeLineManager.timeLineController.nextFrame === SpeedStatus.Up1X ? true : false
                onClicked:  {
                    isProcessing = true
                    timeLineManager.timeLineController.nextFrame = SpeedStatus.Up1X
                    antiSpamTimer.start()
                }
            }

            ControlButton {
                text: "2X"
                Layout.fillWidth: true
                Layout.fillHeight: true
                enabled: !isProcessing
                buttonType: timeLineManager.timeLineController.nextFrame === SpeedStatus.Up2X ? true : false
                onClicked:  {
                    isProcessing = true
                    timeLineManager.timeLineController.nextFrame = SpeedStatus.Up2X
                    antiSpamTimer.start()
                }
            }

            ControlButton {
                text: "4X"
                Layout.fillWidth: true
                Layout.fillHeight: true
                enabled: !isProcessing
                buttonType: timeLineManager.timeLineController.nextFrame === SpeedStatus.Up4X ? true : false
                onClicked:  {
                    isProcessing = true
                    timeLineManager.timeLineController.nextFrame = SpeedStatus.Up4X
                    antiSpamTimer.start()
                }
            }

            ControlButton {
                text: "8X"
                Layout.fillWidth: true
                Layout.fillHeight: true
                enabled: !isProcessing
                buttonType: timeLineManager.timeLineController.nextFrame === SpeedStatus.Up8X ? true : false
                onClicked:  {
                    isProcessing = true
                    timeLineManager.timeLineController.nextFrame = SpeedStatus.Up8X
                    antiSpamTimer.start()
                }
            }
        }

    }

}