from PySide6.QtQuick import QQuickPaintedItem, QQuickItem
from PySide6.QtCore import Property, Signal, Slot, Qt, QEvent, QTimer,QObject
from PySide6.QtWidgets import QMenu
from PySide6.QtGui import Q<PERSON><PERSON>ter, QPixmap,QCursor
from PySide6.QtQml import QJ<PERSON><PERSON>ue
from typing import Callable, List
from src.styles.style import Style, Theme
from src.common.camera.video_player_manager import video_player_manager
from src.common.camera.player import CameraState, Player
from src.common.controller.controller_manager import controller_manager,Controller
from src.common.model.camera_model import camera_model_manager,CameraModel
from src.common.controller.main_controller import main_controller
from src.common.qml.models.timelinecontroller import TimeLineController,SpeedStatus
from src.common.qml.models.common_enum import CommonEnum
from src.common.model.record_model import record_model_manager, RecordData
from src.presentation.camera_screen.export_video_dialog import ExportVideoDialog
from src.common.qml.models.export_video_data import ExportVideoData
from src.common.threads.sub_thread import SubThread
from queue import Queue
import time
import uuid
import logging
import threading
logger = logging.getLogger(__name__)


class FrameModel(QQuickPaintedItem):
    clicked = Signal()
    doubleClicked = Signal()
    rightClicked = Signal()
    frameCountChanged = Signal(int)
    cameraStateChanged = Signal(str)
    # actuallyVisibleChanged = Signal(bool)
    process_player_signal = Signal(QObject)
    itemDataChanged = Signal()
    contentBoundsChanged = Signal(float, float, float, float)  # x, y, width, height
    cameraRestartRequested = Signal(str)  # Signal to request camera restart

    def __init__(self, parent=None):
        super().__init__(parent)
        self.destroyed.connect(self._on_destroyed)
        self.uuid = uuid.uuid4()
        logger.debug(f'FrameModel: {self.uuid}')
        self.process_player_signal.connect(self.process_player_slot)
        self._model = None
        self._q_image = None
        self.player = None
        self.controller = None
        self._grid_row = -1
        self._grid_col = -1
        self._is_playing = False
        self._itemData = None
        self._isSelected = False
        self._frame_count = 0

        self._is_actually_visible = False
        #VideoPlayback
        self.cameraScreen = main_controller.list_parent["CameraScreen"]
        self.timelinecontroller = None
        self.record_data = None
        self.record_url = None
        self.start_duration = 0
        self.end_duration = 0
        self.seek_time = 0
        self.input_queue = Queue()
        self.threads = self.start_threads(1,self.process_data)
        ##################

        # Track previous content bounds to only emit signal on changes
        self._prev_content_bounds_x = 0.0
        self._prev_content_bounds_y = 0.0
        self._prev_content_bounds_width = 0.0
        self._prev_content_bounds_height = 0.0

        self.callback_post_process = None
        self.is_loading_timeline_ui = False
        self.selectedDuration:dict = {"start": None, "end":None}
        # Disable mouse events - let GridItemActionHandler handle them
        self.setAcceptedMouseButtons(Qt.NoButton)
        self.setAcceptHoverEvents(False)

    # @Property(bool, notify=actuallyVisibleChanged)
    # def actuallyVisible(self):
    #     """Property để QML có thể truy cập trạng thái visible thực sự"""
    #     return self._is_actually_visible

    # @Slot(bool)
    # def updateEffectiveVisibility(self, effectivelyVisible):
    #     """Slot để QML cập nhật trạng thái effective visibility"""
    #     if self._is_actually_visible != effectivelyVisible:
    #         self._is_actually_visible = effectivelyVisible
    #         self.actuallyVisibleChanged.emit(effectivelyVisible)

    @Slot("QVariant")
    def register_player(self, model):
        # logger.debug(f'register_player11111111111111111111: {model} - type: {type(model)}')
        if model is not None:
            if isinstance(model, dict):
                logger.debug(f'register_player: A')
                camera_id = model.get('id')
                stream_type = model.get("streamType", 0)  # Default to main stream if not specified
                camera_model = camera_model_manager.get_camera_model(id=camera_id)
                if self.itemData:
                    self.itemData.cameraModel = camera_model
                self.controller = controller_manager.get_controller(server_ip=camera_model.get_property('server_ip'))
                video_player_manager.register_player(self, camera_model, stream_type)
                logger.debug(f'register_player: B')
            if isinstance(model, CameraModel):
                self.controller = controller_manager.get_controller(server_ip=model.get_property("server_ip"))
                # Use MAIN_STREAM as default for better quality
                stream_type = self._itemData.getStreamTypeForCurrentMode()
                logger.debug(f'register_player: stream_type = {stream_type}')
                video_player_manager.register_player(self, model, stream_type)
                if self.itemData:
                    self.itemData.cameraModel = model
                self.record_data = record_model_manager.register_record_data(self)
            elif isinstance(model, QJSValue):
                model_data = model.toVariant()
                camera_id = model_data.get('id')
                stream_type = model_data.get("streamType", 0)  # Default to main stream if not specified
                camera_model = camera_model_manager.get_camera_model(id=camera_id)
                if self.itemData:
                    self.itemData.cameraModel = camera_model
                self.record_data = record_model_manager.register_record_data(self)
                if camera_model is not None:
                    self.controller = controller_manager.get_controller(server_ip=camera_model.get_property('server_ip'))
                    # Convert stream type to StreamCameraType
                    stream_type = CommonEnum.StreamType.MAIN_STREAM if stream_type == 0 else CommonEnum.StreamType.SUB_STREAM
                    video_player_manager.register_player(self, camera_model, stream_type)
            elif isinstance(model, str):
                camera_model = camera_model_manager.get_camera_model(id=model)
                if self.itemData:
                    self.itemData.cameraModel = camera_model
                self.record_data = record_model_manager.register_record_data(self)
                if camera_model is not None:
                    self.controller = controller_manager.get_controller(server_ip=camera_model.get_property('server_ip'))
                    video_player_manager.register_player(self, camera_model, CommonEnum.StreamType.SUB_STREAM)

    @Slot()
    def unregister_player(self):
        try:
            logger.debug(f"[FrameModel] unregister_player called")
            video_player_manager.unregister_player(self)
            record_model_manager.unregister_record_data(self)
        except Exception as e:
            logger.error(f"[FrameModel] Error unregistering video capture: {str(e)}")
            logger.error(f"[FrameModel] Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"[FrameModel] Traceback: {traceback.format_exc()}")


    def paint(self, painter: QPainter):
        if self._q_image is None:
            return

        target_rect = self.boundingRect()
        container_width = target_rect.width()
        container_height = target_rect.height()

        frame_width = self._q_image.width()
        frame_height = self._q_image.height()

        if frame_width <= 0 or frame_height <= 0:
            return

        # Calculate aspect ratios
        frame_aspect = frame_width / frame_height
        container_aspect = container_width / container_height

        # Calculate scale factor and content dimensions (similar to CustomImage logic)
        if frame_aspect > container_aspect:
            # Frame is wider - fit to container width
            scale_factor = container_width / frame_width
            content_width = container_width
            content_height = frame_height * scale_factor
            x_offset = 0
            y_offset = (container_height - content_height) / 2
        else:
            # Frame is taller - fit to container height
            scale_factor = container_height / frame_height
            content_width = frame_width * scale_factor
            content_height = container_height
            x_offset = (container_width - content_width) / 2
            y_offset = 0

        # Save painter state
        painter.save()

        # Enable anti-aliasing for smoother rendering
        painter.setRenderHint(QPainter.Antialiasing, True)
        painter.setRenderHint(QPainter.SmoothPixmapTransform, True)

        # Apply transform: translate to center position, then scale
        painter.translate(x_offset, y_offset)
        painter.scale(scale_factor, scale_factor)

        # Draw original pixmap at (0,0) - painter transform will handle scaling and positioning
        painter.drawPixmap(0, 0, self._q_image)

        # Restore painter state
        painter.restore()

        # Calculate content bounds for signal emission (using ceil for consistency with QML)
        import math
        new_x = int(math.floor(x_offset))
        new_y = int(math.floor(y_offset))
        new_width = int(math.ceil(content_width))
        new_height = int(math.ceil(content_height))

        # Only emit signal if content bounds have changed
        if (self._prev_content_bounds_x != new_x or
            self._prev_content_bounds_y != new_y or
            self._prev_content_bounds_width != new_width or
            self._prev_content_bounds_height != new_height):

            # Update previous values
            self._prev_content_bounds_x = new_x
            self._prev_content_bounds_y = new_y
            self._prev_content_bounds_width = new_width
            self._prev_content_bounds_height = new_height

            # Emit signal for QML to listen and update GridItemBase properties
            self.contentBoundsChanged.emit(new_x, new_y, new_width, new_height)

    @Slot(QPixmap)
    def updateFrame(self, frame):
        if frame is not None:
            self._q_image = frame
            self._frame_count += 1
            self.frameCountChanged.emit(self._frame_count)

            # Không gọi self.update() khi đang animation để tránh repaint không cần thiết
            if self._itemData and hasattr(self._itemData, 'isAnimating') and self._itemData.isAnimating:
                # Skip update during animation to avoid unnecessary repaints
                return

            self.update()

    def mousePressEvent(self, event):
        if event.button() == Qt.RightButton:
            self.rightClicked.emit()
        else:
            self.clicked.emit()

    def mouseDoubleClickEvent(self, event):
        self.doubleClicked.emit()

    @Property(int)
    def gridRow(self):
        return self._grid_row

    @gridRow.setter
    def gridRow(self, row):
        self._grid_row = row

    @Property(int)
    def gridCol(self):
        return self._grid_col

    @gridCol.setter
    def gridCol(self, col):
        self._grid_col = col

    @Property("QVariant")
    def model(self):
        return self._model

    @model.setter
    def model(self, value):
        if self._model != value:
            self._model = value

    def start(self):
        # logger.info(f"✅ [FrameModel] start")
        cameraModel = self.itemData.cameraModel
        self.itemData.cameraName = cameraModel.get_property("name","")
        self.itemData.stateCamIconServer = self.itemData.getStateCamIconServer(cameraModel)
        self.register_player(cameraModel)

    def stop(self):
        self.unregister_player()
        self._q_image = None
        self._frame_count = 0

        # Không gọi self.update() khi đang animation để tránh repaint không cần thiết
        if self._itemData and hasattr(self._itemData, 'isAnimating') and self._itemData.isAnimating:
            # Skip update during animation to avoid unnecessary repaints
            return

        self.update()

    @Property(int, notify=frameCountChanged)
    def frameCount(self):
        return self._frame_count


    @Property(QObject, notify=itemDataChanged)
    def itemData(self):
        return self._itemData

    @itemData.setter
    def itemData(self, value):
        logger.debug(f"[FrameModel] Item data changed: {value}")
        if self._itemData != value:

            # Disconnect old signals if itemData exists
            if self._itemData:
                if hasattr(self._itemData, 'isPlayingChanged'):
                    try:
                        self._itemData.isPlayingChanged.disconnect(self.isPlayingChanged)
                    except (TypeError, RuntimeError):
                        logger.error(f'itemData.setter isPlayingChanged')

            logger.debug(f'itemData.setter: A')
            self._itemData = value
            logger.debug(f'itemData.setter: B')
            # Connect new signals if itemData exists
            if self._itemData:
                if hasattr(self._itemData, 'playerChanged'):
                    self._itemData.playerChanged.connect(self.playerChanged)

                if hasattr(self._itemData, 'switchStreamTypeChanged'):
                    self._itemData.switchStreamTypeChanged.connect(self.switchStreamTypeChanged)

                # ✅ PHASE 3: Connect resize completion signal
                if hasattr(self._itemData, 'resizeCompleted'):
                    self._itemData.resizeCompleted.connect(self.updateResizeAfterLayout)



                if hasattr(self._itemData, 'isPlayingChanged'):
                    self._itemData.isPlayingChanged.connect(self.isPlayingChanged)

            self.itemDataChanged.emit()

    @Slot(int, int)
    def updateResizeAfterLayout(self, width: int, height: int):
        """
        ✅ PHASE 2: Update player resize after fullscreen animation or layout change completes
        Called after fullscreen animation finishes or layout changes are complete
        """
        if self.player and hasattr(self.player, 'update_resize'):
            logger.debug(f"🚀 [LAYOUT] Updating player resize to {width}x{height}")
            self.player.update_resize(width=width, height=height, uuid=self.uuid)

    def isPlayingChanged(self):
        if not self.itemData.isPlaying:
            self.stop()
        else:
            self.start()
    @Slot()
    def clickedChanged(self):
        if self.timelinecontroller is None:
            startTime = time.time()
            self.create_timelinecontroller()
            self.timelinecontroller.openCalendarDialog.connect(self.cameraScreen.openCalendarDialog)
            self.cameraScreen.timeLineManager.timeLineController = self.timelinecontroller
            def callback(data):
                response,dateFrom,dateTo = data
                if response is not None:
                    if response.status_code == 200:
                        data = response.json()
                        record_model_manager.add_records(record_list = data,dateFrom = dateFrom,dateTo = dateTo)
                else:
                    record_model_manager.add_records(record_list = [],dateFrom = dateFrom,dateTo = dateTo)

                if self._itemData and self._itemData.cameraModel:
                    camera_id = self._itemData.cameraModel.get_property("id")
                    camera_model = camera_model_manager.get_camera_model(id=camera_id)
                    self.timelinecontroller.setCameraName(camera_model.get_property("name"))
                    if self.record_data is not None:
                        self.record_data.recordDataChanged.connect(self.recordDataChanged)

                    if self.record_data is None:
                        self.timelinecontroller.setIsTimeLine(False)
                        return
                    camera_data = {
                        record.data.id: record
                        for record in self.record_data.data
                        if record.data.cameraId == camera_id
                    }
                    if camera_data:
                        self.timelinecontroller.setIsTimeLine(False)
                        self.timelinecontroller.setIsTimeLine(True)
                    else:
                        self.timelinecontroller.setIsTimeLine(False)
                    if camera_data:
                        self.timelinecontroller.initData(self.record_data.start_duration, self.record_data.end_duration)
                        self.timelinecontroller.updateRecordDuration(self.record_data)
            def process(cameraIds):
                data = self.controller.api_client.get_videos(cameraIds = cameraIds)
                while self._itemData.isAnimating:
                    logger.info(f'Camera {self._itemData.cameraModel.name} isAnimating {time.time() - startTime}')
                    time.sleep(0.01)
                    if (time.time() - startTime) > 0.5:
                        # isAnimating > 500ms
                        logger.info(f'isAnimating > 500ms')
                        return data
                return data
            if self.controller and self._itemData and self._itemData.cameraModel:
                subThread = SubThread(parent=self.cameraScreen,target=process,args=(self._itemData.cameraModel.get_property("id"),),callback = callback)
                subThread.start()
            else:
                pass
        else:
            self.cameraScreen.timeLineManager.timeLineController = self.timelinecontroller
            
    @Slot()
    def unClickedChanged(self):
        self.cameraScreen.default_timelinecontroller()

    def switchStreamTypeChanged(self,streamType, widget = None):
        if widget == self or widget is None:
            if streamType != self.player.stream_type and self.itemData and self.itemData.cameraModel is not None:
                video_player_manager.unregister_player(self)
                self.streamType = streamType
                video_player_manager.register_player(self, self.itemData.cameraModel, streamType)

                # Set video playback mode when switching to VIDEO_STREAM
                if streamType == CommonEnum.StreamType.VIDEO_STREAM:
                    if self._itemData and hasattr(self._itemData, 'setVideoPlaybackMode'):
                        self._itemData.setVideoPlaybackMode(True)
                else:
                    if self._itemData and hasattr(self._itemData, 'setVideoPlaybackMode'):
                        self._itemData.setVideoPlaybackMode(False)

                if not self.player.isRunning():
                    if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                        self.player.start_thread()

    def playerChanged(self, data):
        if self.player != data:
            if data is None:
                return
            try:
                video_player_manager.unregister_player(self)
            except Exception as e:
                logger.error(f"Error unregistering player: {e}")
            self.player = data
            self.player.connect_status = True
            self.player.register_signal(self)

            # Use actual component size instead of fixed size
            width = int(self.width())
            height = int(self.height())
            if width <= 0 or height <= 0:
                # Use default size if component size is invalid
                width = 1920
                height = 1080

            self.player.update_resize(width=width, height=height, uuid=self.uuid)

            if not self.player.isRunning():
                if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                    self.player.start_thread()

    def process_player_slot(self, player):
        logger.debug(f'process_player_slot: {player} - type: {type(player)}')
        def callback(data):
            pass
        try:
            subThread = SubThread(parent=self,target=self.process_player,args=(player,),callback=callback)
            subThread.start()
        except Exception as e:
            import traceback
            logger.error(f'process_player_slot: Traceback: {traceback.format_exc()}')

    def process_player(self,player:Player):
        try:
            self.player = player
            # setup callback post process
            from src.common.camera.live_stream_player import LiveStreamPlayer
            if isinstance(self.player, LiveStreamPlayer):
                self.player.is_send_mat_frame = self.callback_post_process is not None
            self.player.connect_status = True
            self.player.register_signal(self)
            self._itemData.isCurrentStreamType = self.player.stream_type
            # Use actual component size instead of fixed size
            width = int(self.width())
            height = int(self.height())
            if width <= 0 or height <= 0:
                # Use default size if component size is invalid
                width = 1920
                height = 1080

            self.player.update_resize(width=width, height=height, uuid=self.uuid)

            if not self.player.isRunning():
                if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                    self.player.start_thread()

                # phần xử lý get stream url chỉ gọi khi stream chưa đc chạy và gọi 1 lần mỗi khi khởi tạo thôi. 
                # cơ chế cập nhật đã có ở websocket message_processor.py để update stream url khi có thay đổi rồi
                def update_stream_url(reponse, streamIndex):
                    if self.player is not None:
                        # logger.debug(f'update_stream_url: {reponse} - type: {type(reponse)}')
                        # Giải thích logic:
                        # - Nếu streamIndex trùng với index của item và state là CONNECTED thì lấy url của item đó
                        # - Nếu không thì lấy url của item có state là CONNECTED
                        data = reponse.json()
                        logger.debug(f'update_stream_url: \n{data} \ntype: {type(data)}')
                        # set data to itemData
                        self._itemData.streamUrls = [item["url"] for item in data]
                        # Lọc chỉ các luồng có trạng thái CONNECTED
                        connected_streams = [item for item in data if item["state"] == "CONNECTED" or item["state"] is None]
                        target_stream = None

                        if connected_streams:
                            # Đầu tiên tìm luồng phù hợp với chỉ số được yêu cầu
                            target_stream = next((stream for stream in connected_streams if stream["index"] == streamIndex), None)
                            # Nếu không tìm thấy luồng phù hợp với chỉ số, sử dụng luồng kết nối đầu tiên có sẵn
                            if target_stream is None and len(connected_streams) > 0:
                                target_stream = connected_streams[0]
                            self.player.on_stream_link_changed(target_stream["url"])
                        else:
                            # set None -> DISCONNECTED
                            self.player.on_stream_link_changed(None)
                logger.debug(f'process_player: C')
                # Use appropriate stream index based on stream type
                if self.player.stream_type == CommonEnum.StreamType.MAIN_STREAM or self.player.stream_type == CommonEnum.StreamType.SUB_STREAM:
                    target_stream_type = self.player.stream_type
                    self.controller.get_stream_url_thread(cameraId=self.player.camera_model.id,streamIndex=target_stream_type,callback=update_stream_url)
                elif self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                    self.player.load_media(self.record_url, seek_time=self.seek_time,start_duration=self.start_duration,end_duration=self.end_duration)
                    self.timelinecontroller.register_player(player=self.player)
                    self.player.play_video()
            
            self._itemData.playerChanged.emit(self.player)

            return True  # Return success indicator

        except Exception as e:
            import traceback
            logger.error(f"[VideoModel] Traceback: {traceback.format_exc()}")
            return False  # Return failure indicator

    @Slot("QVariant")
    def share_frame_signal(self, data):
        try:
            grab, mat_frame, pixmap_frame = data
            if grab and mat_frame is not None:
                if self.callback_post_process is not None:
                    pixmap_frame = self.callback_post_process(mat_frame)
                    self.updateFrame(pixmap_frame)
            elif grab and pixmap_frame is not None:
                self.updateFrame(pixmap_frame)
        except Exception as e:
            import traceback

    @Slot(str)
    def camera_state_signal(self, camera_state):
        try:
            if self._itemData and hasattr(self._itemData, 'connectionState'):
                current_state = self._itemData.connectionState
                if current_state != camera_state:
                    self._itemData.connectionState = camera_state
                    # Clear frame when camera is stopped
                    if camera_state == "stopped":
                        self._q_image = None
                        self._frame_count = 0
                        self.update()  # Trigger repaint to clear the frame
        except Exception as e:
            logger.error(f'camera_state_signal error: {e}')
            import traceback
            traceback.print_exc()

    def buffering_signal(self, percent):
        temp = int(percent)
        if temp < 100:
            self._itemData.percent = str(temp) + "%"
            self._itemData.connectionState = "buffering"
        else:
            self._itemData.connectionState = "started"

    # VideoPlayback
    @Property(bool)
    def isSelected(self):
        return self._isSelected

    @isSelected.setter
    def isSelected(self, value:bool):
        if self._isSelected != value:
            self._isSelected = value


    def create_timelinecontroller(self):
        self.timelinecontroller = TimeLineController(parent=self.cameraScreen)
        self.timelinecontroller.positionClicked.connect(self.positionClicked)
        self.timelinecontroller.isPlayChanged.connect(self.isPlayChanged)
        self.timelinecontroller.isLiveChanged.connect(self.isLiveChanged)
        self.timelinecontroller.isNextChunkClicked.connect(self.isNextChunkClicked)
        self.timelinecontroller.isPreviousChunkClicked.connect(self.isPreviousChunkClicked)
        self.timelinecontroller.speedStatusChanged.connect(self.speedStatusChanged)
        self.timelinecontroller.nextFrameChanged.connect(self.nextFrameChanged)
        self.timelinecontroller.hoverPositionChanged.connect(self.hoverPositionChanged)
        self.timelinecontroller.selectedDurationChanged.connect(self.selectedDurationChanged)
        self.timelinecontroller.showMenu.connect(self.show_menu)
        return self.timelinecontroller
    
    def recordDataChanged(self):
        if self.timelinecontroller is not None:
            if not self.timelinecontroller.isTimeLine:
                self.timelinecontroller.setIsTimeLine(True)
                self.timelinecontroller.initData(self.record_data.start_duration, self.record_data.end_duration)
            self.timelinecontroller.updateRecordDuration(self.record_data)

    def positionClicked(self, position):
        """Handle timeline position click with time sync"""
        self._itemData.connectionState = "buffering"
        self._itemData.percent = str(0) + "%"
        position = int(position)
        self.input_queue.put(position)

    def process_data(self)-> None:
        while True:
            position = self.input_queue.get()
            if position is None:
                break
            self.previous_time = None
            self.current_time = None
            self.target_time = None
            if self.record_data is not None and self.player is not None and self.itemData and self.itemData.cameraModel is not None:
                try:
                    data = self.record_data.get_record(position, self.itemData.cameraModel.get_property("id"))
                    if data is not None:
                        record, duration_time_need_seek, duration_to_move = data
                        # source = self.player.get_current_url()
                        self.record_url = record.data.url
                        self.seek_time = duration_time_need_seek
                        self.start_duration = record.data.start_duration
                        self.end_duration = record.data.end_duration
                        if duration_to_move != -1:
                            self.timelinecontroller.shift_for_duration(duration_to_move)
                        if not self.input_queue.empty():
                            continue
                        if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                            self._itemData.switchStreamTypeChanged.emit(CommonEnum.StreamType.VIDEO_STREAM,self)
                            self.timelinecontroller.isNextChunk = True
                            self.timelinecontroller.isNextFrame = True
                        else:
                            source = self.player.get_current_url()
                            if (record.data.url is not None and record.data.url != source):
                                self.timelinecontroller.isNextChunk = True
                                self.timelinecontroller.isNextFrame = True
                                self.player.stop_video()
                                self.player.load_media(record.data.url, seek_time=duration_time_need_seek,start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                                self.player.play_video()
                            else:
                                if self.player.get_length() != 0 and self.player.is_playing():
                                    position_seek = float(duration_time_need_seek)/float(self.player.get_length())
                                    self.player.set_position(position_seek)
                                elif self.player.get_length() != 0 and not self.player.is_playing():
                                    self.player.play_video()
                                    position_seek = float(duration_time_need_seek)/float(self.player.get_length())
                                    self.player.set_position(position_seek)

                        self.timelinecontroller.isLive = False
                        self.timelinecontroller.positionBubble = True
                except ValueError as e:
                    logger.error(f"Error parsing position timestamp: {e}")
                except Exception as e:
                    logger.error(f"Error handling position click: {e}")
            self.input_queue.task_done()

    def isPlayChanged(self):
        if self.timelinecontroller.isPlay:
            if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                self.player.start_video()
            else:
                self.player.play_live()
        else:
            if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                self.player.pause_video()
            else:
                self.player.pause_live()

    def isLiveChanged(self):
        if self.timelinecontroller.isLive:
            self.timelinecontroller.positionBubble = False

            # Clear video playback mode and switch to appropriate live stream
            if self._itemData:
                self._itemData.setVideoPlaybackMode(False)

                # Use getStreamTypeForCurrentMode for live stream switching (size is stable here)
                target_stream_type = self._itemData.getStreamTypeForCurrentMode() if hasattr(self._itemData, 'getStreamTypeForCurrentMode') else CommonEnum.StreamType.MAIN_STREAM
                self.switchStreamTypeChanged(target_stream_type, self)

    def isNextChunkClicked(self):
        self.next_chunk_signal('')

    def isPreviousChunkClicked(self):
        self.previous_chunk_signal('')

    def speedStatusChanged(self,data):
        logger.debug(f'speedStatusChanged = {data}')
        speed = 0
        if data == 1:
            if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Pause:
                speed = SpeedStatus.SpeedEnum.Up1X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
                speed = SpeedStatus.SpeedEnum.Up2X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
                speed = SpeedStatus.SpeedEnum.Up4X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
                speed = SpeedStatus.SpeedEnum.Up8X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
                speed = SpeedStatus.SpeedEnum.Up8X
        elif data == -1:
            if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
                # speed = SpeedStatus.SpeedEnum.Pause
                # self.timelinecontroller.isPlay = False
                return
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
                speed = SpeedStatus.SpeedEnum.Up1X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
                speed = SpeedStatus.SpeedEnum.Up2X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
                speed = SpeedStatus.SpeedEnum.Up4X
            else:
                speed = SpeedStatus.SpeedEnum.Pause
                self.timelinecontroller.isPlay = False
                return
        self.timelinecontroller.isPlay = True
        if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM and self.player.is_playing():
            try:
                self.player.set_speed(speed)
                self.timelinecontroller.nextFrame = speed
            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def nextFrameChanged(self):

        speed = 0
        if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Pause:
            speed = SpeedStatus.SpeedEnum.Up1X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
            speed = SpeedStatus.SpeedEnum.Up1X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
            speed = SpeedStatus.SpeedEnum.Up2X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
            speed = SpeedStatus.SpeedEnum.Up4X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
            speed = SpeedStatus.SpeedEnum.Up8X

        if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM and self.player.is_playing():
            try:
                self.player.set_speed(speed)
                # self.timelinecontroller.nextFrame = speed
            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def hoverPositionChanged(self, position):
        pass

    def selectedDurationChanged(self, startTime, endTime):
        logger.info(f'selectedDurationChanged = {startTime,endTime}')
        self.selectedDuration["start"] = startTime
        self.selectedDuration["end"] = endTime

    def show_menu(self, position):
        logger.debug(f'showMenu = {position, type(position)}')
        def handle_clear_selection():
            logger.debug(f'handle_clear_selection')
            self.timelinecontroller.clearSelectionChanged.emit()
        def handle_zoom_to_selection():
            logger.debug(f'handle_zoom_to_selection')
            self.timelinecontroller.zoomToSelectionChanged.emit()
        def handle_export_video():
            logger.debug(f'handle_export_video')
        menu = QMenu()
        # menu.setStyleSheet(Style.StyleSheet.context_menu)
        menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        clear_selection = menu.addAction(self.tr("Clear Selection"))
        clear_selection.triggered.connect(handle_clear_selection)
        zoom_to_selection = menu.addAction(self.tr('Zoom to Selection'))
        zoom_to_selection.triggered.connect(handle_zoom_to_selection)
        export_video = menu.addAction(self.tr('Export video'))
        export_video.triggered.connect(self.handle_export_video)
        current_mouse = QCursor.pos()
        logger.debug(f'current_mouse = {current_mouse}')
        position_of_mouse = self.mapFromGlobal(current_mouse)
        logger.debug(f'position_of_mouse = {position_of_mouse}')
        menu.exec_(self.mapToGlobal(position_of_mouse).toPoint())
        self.timelinecontroller.closeMenu()

    def handle_export_video(self):
        logger.info(f'handle_export_video {self.selectedDuration}')
        if self.selectedDuration["start"] is None or self.selectedDuration["end"] is None:
            logger.error("thời gian export video không hợp lệ")
            return
        data = {
            "start": self.selectedDuration["start"],
            "end": self.selectedDuration["end"],
            "cameraId": self._itemData.cameraModel.get_property("id")
        }
        dialog = ExportVideoDialog(exportVideoData=ExportVideoData(data=data))
        result = dialog.exec()

    def next_chunk_signal(self, data):
        logger.debug(f'next_chunk_signal = {self.record_data}')
        if self.record_data is not None and self.player is not None and self.itemData and self.itemData.cameraModel is not None:
            try:
                data = self.record_data.get_next_record(self.end_duration, self.itemData.cameraModel.get_property("id"))
                if data is not None:
                    record = data
                    # source = widget.record_capture.get_current_url()
                    self.record_url = record.data.url
                    self.seek_time = None
                    self.start_duration = record.data.start_duration
                    self.end_duration = record.data.end_duration
                    old_duration = self.player.current_duration
                    self.player.stop_video()
                    self.player.load_media(record.data.url, start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                    self.player.play_video()
                    # self.timelinecontroller.scrollbarPositionChanged.emit()
                    self.timelinecontroller.shift_for_duration(self.start_duration - old_duration)
                    self.timelinecontroller.scrollbarPositionChanged.emit()
                    # self.timelinecontroller.isLive = False
                    # self.timelinecontroller.positionBubble = True
                else:
                    pass
                    # vào chế độ live
                    logger.debug(f'next_chunk_signal1')
                    self.timelinecontroller.isLive = True
                    self.timelinecontroller.positionBubble = False
                    self.timelinecontroller.isNextChunk = False
                    self.timelinecontroller.isNextFrame = False
                    self.timelinecontroller.nextFrame = SpeedStatus.SpeedEnum.Up1X

            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def previous_chunk_signal(self, data):
        logger.debug(f'previous_chunk_signal = {self.record_data}')
        if self.record_data is not None and self.player is not None and self.itemData and self.itemData.cameraModel is not None:
            if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                try:
                    data = self.record_data.get_previous_record(self.start_duration, self.itemData.cameraModel.get_property("id"))
                    logger.debug(f'previous_chunk_signal = {data}')
                    if data is not None:
                        record = data
                        # source = widget.record_capture.get_current_url()
                        self.record_url = record.data.url
                        self.seek_time = None
                        self.start_duration = record.data.start_duration
                        self.end_duration = record.data.end_duration
                        self.player.stop_video()
                        self.player.load_media(record.data.url, start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                        self.player.play_video()
                        # self.timelinecontroller.isLive = False
                        # self.timelinecontroller.positionBubble = True
                    # else:
                    #     pass
                    #     # vào chế độ live
                    #     logger.debug(f'next_chunk_signal1')
                    #     self.timelinecontroller.isLive = True
                    #     self.timelinecontroller.positionBubble = False
                    #     self.timelinecontroller.isNextChunk = False
                    #     self.timelinecontroller.isNextFrame = False
                except ValueError as e:
                    logger.error(f"Error parsing position timestamp: {e}")
                except Exception as e:
                    logger.error(f"Error handling position click: {e}")

    def start_threads(self,number: int, target: Callable, *args) -> List[threading.Thread]:
        threads = []
        for _ in range(number):
            thread = threading.Thread(target=target, args=args)
            thread.daemon = True
            threads.append(thread)
            thread.start()
        return threads


    def _on_destroyed(self, *args):
        logger.info(f"FrameModel C++ object destroyed")
    def __del__(self):
        logger.info(f"FrameModel Python wrapper deleted")