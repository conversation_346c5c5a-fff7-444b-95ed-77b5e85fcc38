from PySide6.QtCore import QObject, Property, Signal, Slot, QDateTime, QEnum
from PySide6.QtGui import QFont,QCursor
from PySide6.QtWidgets import QMenu
from enum import IntEnum
from datetime import datetime, timed<PERSON>ta
from typing import List
from src.common.qml.models.ruler_context import RulerContext
from src.common.qml.models.timestep2 import TimeStep2,State
from src.common.qml.models.durationpresent import DurationPresent
from src.common.qml.models.timestepnode import TimeStepNode,TimeStepValue
from src.common.qml.models.timesteptree import TimeStepTree
from src.common.qml.models.calendarmodel import CalendarModel
from src.common.camera.live_stream_player import LiveStreamPlayer
from src.common.camera.video_player import VideoPlayer
from src.common.controller.main_controller import main_controller
from src.common.threads.sub_thread import SubThread
from src.common.model.record_model import Record,RecordModel,record_model_manager,RecordData
from src.utils.utils import Utils
from src.styles.style import Style
import re
import logging
logger = logging.getLogger(__name__)
DELAY_UPDATE_X = 100  # ms
DEFAULT = 0
class SpeedStatus(QObject):  
    # @QEnum
    class SpeedEnum(IntEnum):
        Pause = 0
        Up1X = 1
        Up2X = 2
        Up4X = 3
        Up8X = 4
        Down1X = 5
        Down2X = 6
        Down4X = 7
        Down8X = 8
    QEnum(SpeedEnum)   
class TimeLineController(QObject):
    # Signals
    ruleWidthChanged = Signal()
    viewWidthChanged = Signal()
    isTimeLineChanged = Signal()
    cameraNameChanged = Signal()
    viewXChanged = Signal()
    isMaximumScaleChanged = Signal()
    mouseXChanged = Signal()
    timeStep2sChanged = Signal()
    recordDurationsChanged = Signal()
    relativePositionChanged = Signal()
    # positionChanged = Signal()
    pressedXChanged = Signal()
    isMenuOpenedChanged = Signal()
    fontChanged = Signal()
    selectedDurationViewChanged = Signal()
    themeChanged = Signal()
    hoverPositionChanged = Signal(str, float)
    selectedDurationChanged = Signal(str, str)
    positionBubbleChanged = Signal()
    showClockChanged = Signal()
    showMenu = Signal(str)
    positionClicked = Signal(str)
    positionChanged = Signal(str)
    isLiveChanged = Signal()
    isLiveClicked = Signal()
    isSyncChanged = Signal()
    isCalendarChanged = Signal()
    isPlayChanged = Signal()
    isNextFrameChanged = Signal()
    isNextChunkChanged = Signal()
    isNextChunkClicked = Signal()
    isPreviousChunkChanged = Signal()
    isPreviousChunkClicked = Signal()
    isProcessingChanged = Signal()
    nextFrameChanged = Signal()
    speedStatusChanged = Signal(int)
    openCalendarDialog = Signal(QObject)
    dateSelected = Signal(str)
    scrollbarPositionChanged = Signal()
    calendarModelChanged = Signal()
    clearSelectionChanged = Signal()
    zoomToSelectionChanged = Signal()
    MIN_DURATION = Utils.D1H
    MAX_DURATION = 12 * Utils.D1D
    FPS = 24
    DELTA_TIME = 1000/FPS
    def __init__(self, parent=None) -> None:
        super().__init__(parent)
        self.camera_id = None
        self.current_duration = {}
        self.start_time = None
        self.end_time = None
        self._isTimeLine = False
        self._cameraName = ""
        self._ruleWidth = 0.0
        self._viewWidth = 0.0
        self._viewX = 0.0
        self._isMaximumScale = False
        self._mouseX = 0.0
        self._timeStep2s = []
        self._recordDurations = []
        self._relativePosition = 0.0
        self._position = ""
        self._pressedX = 0.0
        self._isMenuOpened = False
        self._dateTimeFont = QFont()
        self._positionFont = QFont()
        self.cachedRelativePosition = 0
        self._theme = "light"
        self._positionBubble = False
        self._showClock = True
        self._ctx = RulerContext(self)
        self._cachedRelativePosition = 0
        self._selectedDuration = DurationPresent(self._ctx,recordModel=None)
        self._lastUpdateTime = 0
        # self._updateXTimer = QTimer(self)
        self._lastXValue = 0
        self._positionFont.setPixelSize(12)
        # self._positionFont.setBold(True)
        self._isLive = True
        self._isSync = False
        self._isCalendar = False
        self._isPlay = True
        self._isNextFrame = False
        self._isNextChunk = False
        self._isPreviousChunk = True
        self._isProcessing = False
        self._speedStatus = SpeedStatus.SpeedEnum.Up1X
        self._nextFrame = SpeedStatus.SpeedEnum.Up1X
        self.player = None
        self.count = 0
        self._calendarModel = CalendarModel(self._ctx)
        self.timeLineThread = None
        # self._updateXTimer.setSingleShot(True)
        # self._updateXTimer.timeout.connect(self.updateX)
        self.setupConnections()
        self.updateTimer(True)
        # self.start_subcribe()

    def register_player(self,player:VideoPlayer = None):
        if self.player != player and self.player is not None:
            self.player.timelinecontroller = None
            self.player = player
            self.player.timelinecontroller = self
        else:
            self.player = player
            self.player.timelinecontroller = self
            
    # def start_subcribe(self):
    #     logger.debug(f'start_subcribe = {self.camera_id}')

    def updateX(self):
        self._ctx.setBase(self._lastXValue)  # Gọi phương thức setBase trên đối tượng ctx
        self._lastUpdateTime = QDateTime.currentMSecsSinceEpoch()  # Lấy thời gian hiện tại

    def setupConnections(self) -> None:
        """Set up all signal connections"""
        # Connect context signals
        self._ctx.baseChanged.connect(self.ruleWidthChanged)
        # self._ctx.xChanged.connect(self.viewXChanged)  # Commented out as in C++
        self._ctx.isMaximumScaleChanged.connect(self.isMaximumScaleChanged)
        
        # Connect internal signals
        self.ruleWidthChanged.connect(self.relativePositionChanged)
        self.viewXChanged.connect(self.relativePositionChanged)
        
        # Connect position change for time bubble updates
        self._ctx.positionChanged.connect(self.slotPositionChanged)
        
        # Connect base change to refresh timesteps
        self._ctx.baseChanged.connect(self.refreshTimeStep2s)
        self._calendarModel.selectedDatesChanged.connect(self.selectedDatesChanged)
        self._calendarModel.selectedHoursChanged.connect(self.selectedHoursChanged)

        main_controller.theme_change_signal.connect(self.themeChanged)

    def updateTimer(self,value: bool):
        if value:
            if not self._ctx._delay_timer.isActive():
                self._ctx._delay_timer.start()
            if not self.showClock:
                self.showClock = True
        else:
            if self._ctx._delay_timer.isActive():
                self._ctx._delay_timer.stop()
            if self.showClock:
                self.showClock = False

    def slotPositionChanged(self):
        self.positionChanged.emit(Utils.convertMilliSecondTimeToString(self._ctx.position()))

    @Property(bool, notify=isLiveChanged)
    def isLive(self):
        return self._isLive

    @isLive.setter
    def isLive(self, value: bool):
        self._isLive = value
        self.isLiveChanged.emit() 

    @Property(bool,notify=isSyncChanged)
    def isSync(self):
        return self._isSync

    @isSync.setter
    def isSync(self, value: bool):
        self._isSync = value
        self.isSyncChanged.emit() 

    @Property(bool, notify=isCalendarChanged)
    def isCalendar(self):
        return self._isCalendar

    @isCalendar.setter
    def isCalendar(self, value: bool):
        self._isCalendar = value
        self.isCalendarChanged.emit() 

    @Property(bool,notify=isPlayChanged)
    def isPlay(self):
        return self._isPlay

    @isPlay.setter
    def isPlay(self, value: bool):
        if self._isPlay != value:
            self._isPlay = value
            self.isPlayChanged.emit() 

    @Property(bool, notify=isNextFrameChanged)
    def isNextFrame(self):
        return self._isNextFrame

    @isNextFrame.setter
    def isNextFrame(self, value: bool):
        self._isNextFrame = value
        self.isNextFrameChanged.emit() 

    @Property(bool, notify=isNextChunkChanged)
    def isNextChunk(self):
        return self._isNextChunk

    @isNextChunk.setter
    def isNextChunk(self, value: bool):
        self._isNextChunk = value
        self.isNextChunkChanged.emit() 

    @Property(int, notify=nextFrameChanged)
    def nextFrame(self):
        return self._nextFrame

    @nextFrame.setter
    def nextFrame(self, value: int):
        if self._nextFrame != value:
            self._nextFrame = value
            self.nextFrameChanged.emit() 

    @Property(bool, notify=isPreviousChunkChanged)
    def isPreviousChunk(self):
        return self._isPreviousChunk

    @isPreviousChunk.setter
    def isPreviousChunk(self, value: bool):
        self._isPreviousChunk = value
        self.isPreviousChunkChanged.emit() 

    @Property(int, notify=speedStatusChanged)  # Thêm notify cho speed_status
    def speedStatus(self):
        return self._speedStatus

    @speedStatus.setter
    def speedStatus(self, value: int):
        # if self._speedStatus != value:
        self._speedStatus = value
        self.speedStatusChanged.emit()  

    @Property(bool, notify=showClockChanged)
    def showClock(self):
        return self._showClock
    
    @showClock.setter
    def showClock(self,value: bool):
        if self._showClock != value:
            self._showClock = value
            self.showClockChanged.emit()

    def onPositionChanged(self,duration):
        self.count +=1
        if self.count%2 == 0:
            # self.setPosition(duration)
            self._ctx.updatePosition(duration)

    @Property(bool, notify=positionBubbleChanged)
    def positionBubble(self):
        return self._positionBubble
    
    @positionBubble.setter
    def positionBubble(self,value: bool):
        if self._positionBubble != value:
            self._positionBubble = value
            self.positionBubbleChanged.emit()

    @Property(bool, notify=isTimeLineChanged)
    def isTimeLine(self):
        return self._isTimeLine

    def setIsTimeLine(self, value: bool):
        if self._isTimeLine != value:
            self._isTimeLine = value
            self.isTimeLineChanged.emit()

    @Property(str, notify=cameraNameChanged)
    def cameraName(self):
        return self._cameraName
    
    def setCameraName(self, name):
        if self._cameraName != name:
            self._cameraName = name
            self.cameraNameChanged.emit()

    @Property(CalendarModel, notify=calendarModelChanged)
    def calendarModel(self):
        return self._calendarModel
    
    @calendarModel.setter
    def calendarModel(self, value: CalendarModel):
        if self._calendarModel != value:
            self._calendarModel = value
            self.calendarModelChanged.emit()

    @Property(bool, notify=isProcessingChanged)
    def isProcessing(self):
        return self._isProcessing

    @isProcessing.setter
    def isProcessing(self, value: bool):
        self._isProcessing = value
        self.isProcessingChanged.emit() 

    def viewWidth(self):
        return self._ctx.visibleWidth()

    def setViewWidth(self, value):
        self._ctx.setVisibleWidth(value)

    viewWidth = Property(float, viewWidth, setViewWidth, notify=viewWidthChanged)

    @Property(bool, notify=isMaximumScaleChanged)
    def isMaximumScale(self) -> bool:
        """
        Check if the scale is at maximum
        
        Returns:
            bool: True if scale is at maximum, False otherwise
        """
        return self._ctx.isMaximumScale()
    isMaximumScale = Property(bool, isMaximumScale, notify=isMaximumScaleChanged)

    def mouseX(self):
        return self._mouseX

    def setMouseX(self, value):
        if self._mouseX != value:
            self._mouseX = value
            self.mouseXChanged.emit()
        if self._ctx.totalTime() != 0:
            self.hoverPositionChanged.emit(
                Utils.convertMilliSecondTimeToString(
                    self._ctx.positionFromMouseX(value)
                ),
                value
            )
    mouseX = Property(float, mouseX, setMouseX, notify=mouseXChanged)

    def getTimeStep2s(self):
        return self._timeStep2s

    def appendTimeStep(self, item):
        """Thêm một phần tử mới vào danh sách."""
        if isinstance(item, TimeStep2):
            self._timeStep2s.append(item)
            self.timeStep2sChanged.emit()  # Thông báo danh sách đã thay đổi

    def clearTimeSteps(self):
        """Xóa toàn bộ danh sách."""
        self._timeStep2s.clear()
        self.timeStep2sChanged.emit()

    timeStep2s = Property(list, getTimeStep2s, notify=timeStep2sChanged)

    @Property(float, notify=relativePositionChanged)
    def relativePosition(self) -> float:
        """
        Get the relative width from the absolute start position
        
        Returns:
            float: The relative position value
        """
        return self._ctx.relativeWidthFromAbsoluteStart()

    @Property(str, notify=positionChanged)
    def position(self) -> str:
        """
        Get the current position as a formatted time string
        
        Returns:
            str: Formatted time string. If no offset, returns simple time string.
                Otherwise, returns formatted datetime string.
        """
        if not self._ctx.offset():
            return Utils.convertTimeToString(self._ctx.position())
        
        return QDateTime.fromMSecsSinceEpoch(
            self._ctx.position()
        ).toString("hh:mm:ss AP")
    
    def setPosition(self, position: int) -> None:
        """
        Set the current position and emit relevant signals
        
        Args:
            position: New position value in milliseconds
        """
        if self._ctx.setPosition(position):
            pass
            self.positionChanged.emit(
                Utils.convertMilliSecondTimeToString(position)
            )
            
            # Check if relative position changed
            if self.cachedRelativePosition != self.relativePosition:
                self.cachedRelativePosition = self.relativePosition
                self.relativePositionChanged.emit()

    def pressedX(self) -> float:
        logger.debug(f'pressedX')
        """
        Get the pressed X position relative to absolute start
        
        Returns:
            float: The relative width from absolute start
        """
        return self._ctx.relativeWidthFromAbsoluteStart()

    def setPressedX(self, value: float) -> None:
        logger.debug(f'setPressedX')
        """
        Set the pressed X position and emit relevant signals
        
        Args:
            value: The new pressed X coordinate
        """
        if self._ctx.setPositionFromMouseX(value):
            self.positionClicked.emit(str(self._ctx.position()))
            self.positionChanged.emit(str(self._ctx.position()))
            
            # Check if relative position changed
            current_relative_pos = self.relativePosition
            if self.cachedRelativePosition != current_relative_pos:
                self.cachedRelativePosition = current_relative_pos
                self.relativePositionChanged.emit()


    pressedX = Property(float, pressedX, setPressedX, notify=pressedXChanged)

    @Property(bool, notify=isMenuOpenedChanged)
    def isMenuOpened(self) -> bool:
        """
        Check if the menu is currently opened
        
        Returns:
            bool: True if menu is opened, False otherwise
        """
        return self._ctx.isMenuOpened()

    def setIsMenuOpened(self, isOpened: bool) -> None:
        """
        Set the menu open state
        
        Args:
            isOpened: True to open menu, False to close it
        """
        if self._ctx.isMenuOpened() != isOpened:
            self._ctx.setIsMenuOpened(isOpened)
            self.isMenuOpenedChanged.emit()

    isMenuOpened = Property(bool, isMenuOpened, setIsMenuOpened, notify=isMenuOpenedChanged)

    @Property(QFont, notify=fontChanged)
    def dateTimeFont(self) -> QFont:
        """
        Get the datetime font
        
        Returns:
            QFont: The font used for datetime display
        """
        return self._dateTimeFont

    @Property(QFont, notify=fontChanged)
    def positionFont(self) -> QFont:
        """
        Get the position font
        
        Returns:
            QFont: The font used for position display
        """
        return self._positionFont

    @Property(DurationPresent, notify=selectedDurationViewChanged)
    def selectedDuration(self):
        return self._selectedDuration

    def setDuration(self, duration: int, offset: int = 0) -> None:
        """
        Set the duration and offset for the timer playback
        
        Args:
            duration: Total duration in milliseconds
            offset: Time offset in milliseconds (default: 0)
        """
        if self._ctx.setTotalTime(duration):
            self._ctx.setOffset(offset)
            self.initTimeStep2s()

    def context(self) -> RulerContext:
        """
        Get the RulerContext instance
        
        Returns:
            RulerContext: The ruler context object
        """
        return self._ctx

    @Property(list, notify=recordDurationsChanged)
    def recordDurations(self):
        return self._recordDurations

    @recordDurations.setter
    def recordDurations(self, value):
        if self._recordDurations != value:
            self._recordDurations = value
            self.recordDurationsChanged.emit()

    @Property(DurationPresent, notify=selectedDurationViewChanged)
    def selectedDuration(self) -> DurationPresent:
        """
        Get the currently selected duration
        
        Returns:
            DurationPresent: The selected duration object
        """
        return self._selectedDuration
    
    
    @Slot(float, float)
    def onSelectedDurationChanged(self, start_x: float, width: float) -> None:
        """
        Handle changes in selected duration
        
        Args:
            start_x: Starting X coordinate
            width: Width of the selection
        """
        # logger.debug(f'onSelectedDurationChanged = {Utils.convertMilliSecondTimeToString(
        #         self._ctx.positionFromMouseX(start_x)
        #     )} - {Utils.convertMilliSecondTimeToString(
        #         self._ctx.positionFromMouseX(start_x + width)
        #     )}')
        if width != 0:
            self.selectedDurationChanged.emit(
                Utils.convertDurationToString(
                    self._ctx.positionFromMouseX(start_x)
                ),
                Utils.convertDurationToString(
                    self._ctx.positionFromMouseX(start_x + width)
                )
            )

    @Slot(float)
    def onShowMenu(self, x: float) -> None:
        print(f"onShowMenu")
        """
        Handle menu show request
        
        Args:
            x: X coordinate where menu should be shown
        """
        self._ctx.setIsMenuOpened(True)
        self.showMenu.emit(
            Utils.convertMilliSecondTimeToString(
                self._ctx.positionFromMouseX(x)
            )
        )

    @Slot(float, result=str)
    def getHoverPosition(self, x: float):
        """
        Get the time string for a hover position
        
        Args:
            x: X coordinate of hover position
            
        Returns:
            str: Formatted time string for the hover position
        """
        hover_position = self._ctx.positionFromMouseX(x)
        if not self._ctx.offset():
            return Utils.convertTimeToString(hover_position)
        return QDateTime.fromMSecsSinceEpoch(hover_position).toString("hh:mm:ss AP")
    
    @Slot(float, float)
    def updateRule(self, width: float, x: float) -> None:
        """
        Update the ruler base with new width and position
        
        Args:
            width: New width for the ruler
            x: New x position for the ruler
        """
        self._ctx.setBase(width, x)

    def shift_for_duration(self, duration:int):
        if duration == 0: 
            return
        self._ctx.shift_for_duration(duration)

    @Property(float, notify=ruleWidthChanged)
    def ruleWidth(self) -> float:
        """
        Get the total width of the ruler
        
        Returns:
            float: The ruler's width
        """
        return self._ctx.width()

    @Property(float, notify=viewXChanged)
    def viewX(self) -> float:
        """
        Get the current X position of the view
        
        Returns:
            float: The view's X position
        """
        return self._ctx.x()
    

    def updateRecordDurations(self, record_data:RecordData = None) -> None:
        self._recordDurations.clear()
        valid_count = 0
        for item in record_data.data:
            if item.data.end_duration is not None:
                record_duration = DurationPresent(self._ctx, recordModel=item)
                self._recordDurations.append(record_duration)
                valid_count += 1
        self.recordDurationsChanged.emit()

    def zoomTo(self, startPos: int, endPos: int) -> bool:
        """
        Zoom the view to a specific range
        
        Args:
            startPos: Start position in milliseconds
            endPos: End position in milliseconds
            
        Returns:
            bool: True if zoom was successful, False otherwise
        """
        visible_duration = endPos - startPos
        if visible_duration <= 0:
            print("Debug: visibleDuration <= 0")
            return False

        # Visible duration is greater or equal to total duration
        if visible_duration >= self._ctx.totalTime():
            # Reset view
            print("Warning: Reset view")
            self.updateRule(DEFAULT, 0)
            return False

        # Visible duration is less than total duration
        if startPos < self._ctx.offset():
            startPos = self._ctx.offset()
            visible_duration = endPos - startPos

        if endPos > self._ctx.offset() + self._ctx.totalTime():
            endPos = self._ctx.offset() + self._ctx.totalTime()
            visible_duration = endPos - startPos

        if not self._ctx.isZoomable(startPos, endPos):
            startPos -= visible_duration // 2
            endPos += visible_duration // 2
            return self.zoomTo(startPos, endPos)

        new_rule_width = (self._ctx.visibleWidth() * 
                        (self._ctx.totalTime() / visible_duration))
        new_x = (self._ctx.offset() - startPos) * self._ctx.widthPerMili()
        
        self.updateRule(new_rule_width, new_x)
        return True
    
    def setDateTimeFont(self, font: QFont) -> None:
        """
        Set the datetime font
        
        Args:
            font: New font for datetime display
        """
        if self._dateTimeFont != font:
            self._dateTimeFont = font
            self.fontChanged.emit()

    def setPositionFont(self, font: QFont) -> None:
        """
        Set the position font
        
        Args:
            font: New font for position display
        """
        if self._positionFont != font:
            self._positionFont = font
            self.fontChanged.emit()

    def _delayUpdateX(self, x: float) -> None:
        """
        Update X position with delay to prevent too frequent updates
        
        Args:
            x: New X position value
        """
        print(f"Debug: delayUpdateX: {x}")  # Debug output
        
        current_time = QDateTime.currentMSecsSinceEpoch()
        
        if current_time - self._lastUpdateTime > DELAY_UPDATE_X:
            # If enough time has passed since last update, update immediately
            self._ctx.setBase(DEFAULT, x)
            self._lastUpdateTime = current_time
        else:
            # If too soon, schedule update with timer
            if self._updateXTimer.isActive():
                self._updateXTimer.stop()
                
            self._lastXValue = x
            self._updateXTimer.start(DELAY_UPDATE_X)

    def initTimeStep2s(self) -> None:
        """
        Initialize TimeStep2 objects if they don't exist and refresh them
        """
        if self._ctx.width() == 0 or self._ctx.visibleWidth() == 0:
            return

        if not self._timeStep2s:  # If list is empty
            for _ in range(self._ctx.maximumItemCount()):
                self._timeStep2s.append(TimeStep2(self._ctx))
        
        self.refreshTimeStep2s()
        self.timeStep2sChanged.emit()

    def refreshTimeStep2s(self) -> None:
        """
        Refresh the TimeStep2 objects based on current context state
        """
        # Early return if dimensions or total time are invalid
        if (self._ctx.width() == 0 or 
            self._ctx.visibleWidth() == 0 or 
            self._ctx.totalTime() == 0):
            return

        # Initialize new tree
        absolute_start = self._ctx.absoluteStart
        absolute_stop = self._ctx.absoluteStop
        new_tree = TimeStepTree(
            absolute_stop - absolute_start,
            absolute_start,
            self._ctx.highestUnit,
            self._ctx.smallestUnit
        )

        # Get visible and undefined values
        visible_values = new_tree.captureVisibleTimeStepValues()
        undefined_values = []  # Equivalent to the commented out C++ code

        # Categorize existing steps
        visible_steps = []
        undefined_steps = []
        outside_steps = []
        idle_steps = []

        # Sort existing timesteps by their state
        for step in self._timeStep2s:
            if step.state() == State.Visible:
                visible_steps.append(step)
            elif step.state() == State.UndefinedHidden:
                undefined_steps.append(step)
            elif step.state() in (State.OutsideLeftHidden, 
                                State.OutsideRightHidden):
                outside_steps.append(step)
            elif step.state() == State.Idle:
                idle_steps.append(step)

        # Handle visible steps
        for step in visible_steps:
            visible = False
            is_unit_changed = step.unitByLineType() != step.value().unit()
            found_index = -1

            if step.value().contains(TimeStepValue(absolute_start, absolute_stop, False)):
                visible = True
            else:
                for i, value in enumerate(visible_values):
                    if is_unit_changed:
                        if ((step.value().end == value.end or 
                            step.value().start == value.start) and 
                            step.unitByLineType() == value.unit()):
                            found_index = i
                            visible = True
                            break
                    else:
                        if step.value() == value:
                            found_index = i
                            visible = True
                            break

            if visible:
                if found_index != -1:
                    # Update value if unit changed
                    if is_unit_changed:
                        step.setValue(visible_values[found_index])
                    # Remove value from visible_values
                    visible_values.pop(found_index)
            else:
                if step.value().start > absolute_stop:
                    step.setState(State.OutsideRightHidden)
                elif step.value().end < absolute_start:
                    step.setState(State.OutsideLeftHidden)
                else:
                    step.setState(State.Idle)

        # Handle outside steps
        for step in outside_steps:
            
            visible = any(step.value() == value for value in visible_values)
            
            if visible:
                step.setState(State.Visible)
                # Remove value from visible_values
                visible_values = [v for v in visible_values if v != step.value()]
            else:
                if (step.state() == State.OutsideLeftHidden and 
                    step.value().start > absolute_start):
                    step.setState(State.OutsideRightHidden)
                elif (step.state() == State.OutsideRightHidden and 
                    step.value().end < absolute_stop):
                    step.setState(State.OutsideLeftHidden)
                else:
                    step.setState(State.Idle)

        # Handle idle steps
        for step in idle_steps:
            if visible_values:
                value = visible_values.pop()
                step.setValue(value)
                step.setState(State.Visible)
            elif undefined_values:
                value = undefined_values.pop()
                step.setValue(value)
                step.setState(State.UndefinedHidden)
            else:
                break

        # Clean up
        new_tree.deleteLater()

    # TimeLineController
    def initData(self, startDuration: int, endDuration: int):

        if endDuration <= 0:
            return

        if startDuration >= endDuration:
            print("startTime is greater or equal to endTime")
            return
        self._calendarModel.updateAvailableDateRanges(startDuration, endDuration)
        self._calendarModel.updateAvailableHourRanges(startDuration, endDuration)
        self.setDuration(endDuration - startDuration, startDuration)
        
    def updateRecordDuration(self, record_data = None):
        for item in record_data.data:
            if item.data.end_duration is not None:
                self._calendarModel.updateRecordedDateRanges(item.data.start_duration,item.data.end_duration)
                self._calendarModel.updateRecordedHourRanges(item.data.start_duration,item.data.end_duration)
        self.updateRecordDurations(record_data)

    def selectedDatesChanged(self):
        dateData = self._calendarModel.selectedDates
        if len(dateData) > 0:
            startDate = dateData[0]
            dt = datetime.strptime(startDate._fullDay, "%Y-%m-%d").timestamp()
            startDuration = int(dt * 1000)
            dt = datetime.strptime(dateData[-1]._fullDay, "%Y-%m-%d").timestamp()
            endDuration = int(dt * 1000) + Utils.D1D
            logger.debug(f'selectedDatesChanged {startDate._fullDay,dateData[-1]._fullDay}')
            self._ctx.changeVisibleRange(startDuration,endDuration)
            self.scrollbarPositionChanged.emit()

    def selectedHoursChanged(self):
        dateData = self._calendarModel.selectedDates
        hoursData = self._calendarModel.selectedHours
        if len(dateData) == 1:
            if len(hoursData) > 0:
                dt = datetime.strptime(f"{dateData[0]._fullDay} {hoursData[0]._fullHour}", "%Y-%m-%d %H:%M:%S").timestamp()
                startDuration = int(dt * 1000)
                dt = datetime.strptime(f"{dateData[0]._fullDay} {hoursData[-1]._fullHour}", "%Y-%m-%d %H:%M:%S").timestamp()
                endDuration = int(dt * 1000) + Utils.D1H
                self._ctx.changeVisibleRange(startDuration,endDuration)
                self.scrollbarPositionChanged.emit()

    @Slot(int, int) 
    def updateZoomToSelection(self,x: int,width: int):
        self._ctx.updateZoomToSelection(x,width)
        self.scrollbarPositionChanged.emit()
        
    def setLargestBigUnitWidth(self, value):
        self._ctx.setLargestUnitW(value)

    def setLargestLittleUnitWidth(self, value):
        self._ctx.setLargestSmallUnitW(value)

    def setMaximumVisibleUnitScaledWidth(self, value):
        self._ctx.setMaximumVisibleUnitScaledW(value)

    def syncPosition(self, position):
        self.setPosition(position)

    def zoomTo(self, startPos: str, endPos: str):
        start = QDateTime.fromString(startPos, "yyyy-MM-ddThh:mm:ssZ").toMSecsSinceEpoch()
        end = QDateTime.fromString(endPos, "yyyy-MM-ddThh:mm:ssZ").toMSecsSinceEpoch()
        self.zoomTo(start, end)
        
    @Slot(str, result=str)
    def dateSelected(self, data):
        logger.debug(f'dateSelected = {data}')

    def closeMenu(self):
        self.setIsMenuOpened(False)

    @Slot(str, result=str)
    def get_color_theme_by_key(self, key = None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)
    
    @Slot(str, result=str)
    def get_image_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Image", key)

class TimeLineManager(QObject):
    timeLineControllerChanged = Signal()  # Tín hiệu thông báo thay đổi

    def __init__(self,parent=None):
        super().__init__(parent)
        self._timeLineController = TimeLineController()  # Tạo đối tượng mặc định

    @Property(TimeLineController,notify=timeLineControllerChanged)
    def timeLineController(self):
        return self._timeLineController

    @timeLineController.setter
    def timeLineController(self, value:TimeLineController):
        if self._timeLineController != value:
            if self._timeLineController is not None:
                self._timeLineController.updateTimer(False)
                if self._timeLineController.isCalendar:
                    self._timeLineController.openCalendarDialog.emit(self._timeLineController)
                    self._timeLineController.isCalendar = False
            self._timeLineController = value
            self._timeLineController.updateTimer(True)
            self.timeLineControllerChanged.emit()  # Thông báo cho QML cập nhật
