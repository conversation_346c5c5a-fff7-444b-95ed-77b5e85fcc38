import time
import threading
import av
from PySide6.QtGui import QI<PERSON>, QPixmap
from src.common.model.camera_model import CameraModel
from src.common.camera.pyav_wrapper import PyAVWrapper
from src.common.camera.player import Player,CameraState
from src.common.qml.models.common_enum import CommonEnum
import logging
logger = logging.getLogger(__name__)
logging.basicConfig()
logging.getLogger('libav').setLevel(level=logging.CRITICAL)
av.logging.set_level(av.logging.PANIC)

class LiveStreamPlayer(Player):
    def __init__(self, camera_id=None, camera_model: CameraModel = None, stream_type=CommonEnum.StreamType.MAIN_STREAM, height=0, width=0, uuid = ''):
        super().__init__(camera_id = camera_id,camera_model = camera_model,height = height,width = width,uuid = uuid)
        logger.debug(f'Khởi tạo Player: Camera ID={camera_id}, Stream={stream_type}')
        self.stream_type = stream_type
        self.stream_link = None
        self.connect_status = True
        self.pyav_wrapper = None

        # Event management - optimized flow with 2 events
        self.state_change_event = threading.Event()   # For server state changes
        self.last_connection_error = None

    def on_stream_link_changed(self, new_stream_link):
        logger.debug(f'on_stream_link_changed: {new_stream_link}')
        if new_stream_link is None:
            self.camera_state = CameraState.stopped
            self.camera_state_signal.emit(self.camera_state)
            self.state_change_event.set()
        else:
            self.stream_link = new_stream_link
            self.camera_state = CameraState.connecting
            self.camera_state_signal.emit(self.camera_state)
            self.state_change_event.set()
            self.stream_link_event.set()  # Wake up the thread if waiting
            logger.debug(f'on_stream_link_changed: set stream_link_event')


    def unregister_signal(self,widget = None):
        """Safely unregister widget signals and cleanup if needed"""
        super().unregister_signal(widget)
        if widget is not None:
            try:
                logger.debug(f'unregister_signal: self.registered_widgets {self.registered_widgets}')
                # Handle widget list cleanup
                if len(self.registered_widgets) > 1:
                    for item in self.registered_widgets:
                        if item == widget:
                            self.registered_widgets.remove(item)
                            break
                elif len(self.registered_widgets) == 1:
                    logger.debug(f'Last widget being unregistered {self.stream_type}')
                    self.registered_widgets.remove(widget)
                    self.stop_capture()
                    return True
                
                # Clear any widget-specific cached data
                if hasattr(self, 'list_resize_frame'):
                    self.list_resize_frame.pop(widget.uuid, None)
                    
            except Exception as e:
                logger.debug(f'Error during widget unregistration: {e}')
                
        return False

    def connect_camera_stream(self):
        try:
            logger.debug(f'Camera {self.camera_id}: Attempting PyAV connection to {self.stream_link}')
            
            # Initialize PyAV wrapper with hardware acceleration enabled
            self.pyav_wrapper = PyAVWrapper()
            
            if self.pyav_wrapper is not None:
                # Open the stream - PyAVWrapper will handle hardware acceleration internally
                if not self.pyav_wrapper.open_stream(self.stream_link):
                    logger.debug(f'Camera {self.camera_id}: Failed to open stream')
                    # if len(self.registered_widgets) > 0:
                    #     self.camera_state = CameraState.stopped
                    #     self.camera_state_signal.emit(self.camera_state)
                    return False
                    
                # Get stream info for logging
                stream_info = self.pyav_wrapper.stream_info
                logger.debug(f'Camera {self.camera_id}: Stream opened successfully - Codec: {stream_info["codec"]}, '
                            f'Resolution: {stream_info["width"]}x{stream_info["height"]}, '
                            f'FPS: {stream_info["fps"]}, '
                            f'HW Accel: {stream_info["hw_accel"]}')
                
                self.camera_state = CameraState.connecting
                self.camera_state_signal.emit(self.camera_state)
                return True
            else:
                logger.debug(f'Camera {self.camera_id}: Khởi tạo PyAVWrapper lỗi')
                return False
        except Exception as e:
            logger.debug(f'Camera {self.camera_id}: PyAV connection failed: {str(e)}')
            if len(self.registered_widgets) > 0:
                self.camera_state = CameraState.stopped
                self.camera_state_signal.emit(self.camera_state)
            return False

    def run(self):
        """Optimized main thread loop with 2-event + switch case system"""
        logger.debug(f'Camera {self.camera_id}: START video capture thread (2-event system)')
        while not self._stop_thread:  # Only exit on Component.onDestruction
            # Switch case based on current conditions
            server_state = self.get_server_state()

            logger.debug(f'A Camera {self.camera_id}: Server state = {server_state}')
            if server_state == "DISCONNECTED":
                # Set camera state to stopped when server disconnected
                logger.debug(f'Camera {self.camera_id}: Waiting for state change')
                self.camera_state = CameraState.stopped
                self.camera_state_signal.emit(self.camera_state)
                self.state_change_event.clear()
                self.state_change_event.wait()
                logger.debug(f'camera state B')
                if self._stop_thread:
                    break
                continue
            elif server_state == "CONNECTED":
                if not self.stream_link:
                    logger.debug(f'Camera {self.camera_id}: Waiting for stream link...')
                    self.stream_link_event.clear()
                    self.stream_link_event.wait()
                    if self._stop_thread:
                        break
                    continue
                logger.debug(f'Camera {self.camera_id}: Process video stream')
                self.process_video_stream()
        
    def play_live(self):
        self.is_pause_live = False
        logger.debug(f'Camera {self.camera_id}: Resuming live stream')

    def pause_live(self):
        logger.debug(f'Camera {self.camera_id}: Pausing live stream')
        self.is_pause_live = True

    def process_video_stream(self):
        """Optimized video stream processing - state-aware, no thread stops"""
        if not self.stream_link:
            logger.debug(f'Camera {self.camera_id}: No stream link available')
            return

        # For CONNECTED state - attempt connection
        if self.pyav_wrapper is None:
            self.camera_state = CameraState.connecting
            self.camera_state_signal.emit(self.camera_state)
            connection_successful = self.connect_camera_stream()

            if not connection_successful:
                self.attempt_reconnection()
                return

        # Start frame processing
        logger.debug(f'Camera {self.camera_id}: Starting frame processing')

        # Frame callback for optimized processing
        def on_frame_received(frame_array, _):
            # Check if thread is being stopped - don't process frames or emit signals
            if self._stop_thread:
                return

            if self.connect_status:
                if self.list_resize_frame is None or len(self.list_resize_frame) == 0 or self.is_pause_live:
                    return
                self.camera_state = CameraState.started
                self.camera_state_signal.emit(self.camera_state)
                pixmap_resized = None
                if not self.is_send_mat_frame:
                    pixmap_resized = self.mat_to_q_pixmap(frame_array)
                    frame_array = None
                data = (True, frame_array, pixmap_resized)
                self.share_frame_signal.emit(data)

        exit_success = self.pyav_wrapper.get_frame(
            width=self.max_width_preview_available,
            height=self.max_height_preview_available,
            callback=on_frame_received,
        )

        if not exit_success:
            self.attempt_reconnection()

    def get_server_state(self):
        """Get current server-reported camera state - only CONNECTED or DISCONNECTED"""
        if hasattr(self, 'camera_model') and self.camera_model:
            state = self.camera_model.state
            # Ensure only valid states
            if state in ["CONNECTED", "DISCONNECTED"]:
                return state
            else:
                logger.warning(f'Camera {self.camera_id}: Invalid state {state}, defaulting to DISCONNECTED')
                return "DISCONNECTED"

        logger.warning(f'Camera {self.camera_id}: No camera_model, defaulting to DISCONNECTED')
        return "DISCONNECTED"

    def attempt_reconnection(self):
        """Optimized reconnection - no retry limits, no state checking"""

        # State checking is done in main loop, so just retry
        logger.info(f'Camera {self.camera_id}: Retrying connection immediately')
        self.pyav_wrapper = None

        # Clear events to wake up main loop
        self.state_change_event.clear()
        self.stream_link_event.clear()

        # Apply minimal delay to prevent spam
        time.sleep(0.1)  # 100ms delay only

    def on_server_state_changed(self, new_state):
        """Called when server state changes via WebSocket"""
        # Wake state change event
        self.state_change_event.set()  # Wake main loop waiting on state change

    def stop_capture(self):
        """Complete thread stop - only called on Component.onDestruction"""

        # Set stop flag
        self._stop_thread = True

        # Stop PyAV processing
        if hasattr(self, 'pyav_wrapper') and self.pyav_wrapper is not None:
            self.pyav_wrapper.close_stream()  # Set is_running = False

        # Wake up all waiting operations
        self.stream_link_event.set()   # Wake stream link waiting
        self.state_change_event.set()  # Wake state change waiting

        self.camera_state = CameraState.stopped
        self.camera_state_signal.emit(self.camera_state)

    def start_thread(self):
        logger.debug(f'Camera {self.camera_id}: Starting video capture thread')
        self._stop_thread = False
        if not self.isRunning():
            self.start()

    @staticmethod
    def mat_to_q_pixmap(mat):
        h, w, c = mat.shape
        d = mat.dtype.itemsize
        s = c * w * d
        img = QImage(
            mat, w, h, s, QImage.Format_RGB888).rgbSwapped()
        return QPixmap.fromImage(img)

    def __del__(self):
        """Destructor to ensure cleanup when object is destroyed"""
        try:
            logger.debug(f'Camera {self.camera_id}: Destructor called (optimized)')
            self.stop_capture()
        except Exception as e:
            logger.debug(f'Error during Player destruction: {e}')

    def update_resize(self, width=None, height=None, uuid = '', remove=False, is_fullscreen = False):
        super().update_resize(width, height, uuid, remove, is_fullscreen)
        if self.pyav_wrapper is not None and self.max_width_preview_available is not None and self.max_height_preview_available is not None:
            self.pyav_wrapper.update_size(self.max_width_preview_available, self.max_height_preview_available)