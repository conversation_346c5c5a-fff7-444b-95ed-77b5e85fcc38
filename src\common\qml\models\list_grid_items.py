from PySide6.QtCore import QAbstractListModel, Qt, QModelIndex, Property, Signal
from typing import Dict, List, Any
import logging
# from src.common.model.camera_model import camera_model_manager, CameraModel
from src.common.qml.models.map_controller import MapModel,BuildingModel,FloorModel,floor_manager,map_manager
from src.common.qml.models.camera_grid_item import CameraGridItem
from src.common.qml.models.digital_map_grid_item import DigitalMapGridItem
from src.common.qml.models.floor_map_grid_item import FloorMapGridItem
from src.common.qml.models.event_grid_item import EventGridItem
from src.common.model.event_data_model import EventModel

logger = logging.getLogger(__name__)


class SmartPositionCache:
    """
    🚀 SMART POSITION CACHING: Advanced caching với pre-computation và intelligent algorithms

    ⚡ PERFORMANCE FEATURES:
    1. Pre-computed available slots list
    2. Sequential position tracking
    3. Density-based cache strategies
    4. O(1) cache hits for common patterns
    """

    def __init__(self):
        # 🚀 CORE CACHE DATA
        self._available_slots = []      # Pre-computed available positions [(row, col), ...]
        self._next_sequential = 0       # Next position index for sequential adding
        self._grid_cols = 1            # Current grid dimensions
        self._grid_rows = 1
        self._occupied_positions = set() # Fast O(1) lookup for occupied positions

        # 🚀 CACHE STATE
        self._dirty = True             # Cache needs rebuild
        self._cache_hits = 0           # Performance stats
        self._cache_misses = 0

        # 🚀 INTELLIGENT STRATEGIES
        self._sequential_mode = True   # True when adding items sequentially
        self._last_position = (-1, -1) # Track last added position for pattern detection

    def updateGridDimensions(self, cols: int, rows: int):
        """Update grid dimensions and mark cache dirty"""
        if self._grid_cols != cols or self._grid_rows != rows:
            self._grid_cols = cols
            self._grid_rows = rows
            self._dirty = True

    def updateOccupiedPositions(self, occupied_positions: set):
        """Update occupied positions and mark cache dirty if changed"""
        if self._occupied_positions != occupied_positions:
            self._occupied_positions = occupied_positions.copy()
            self._dirty = True

    def getNextPosition(self, item_cols: int = 1, item_rows: int = 1) -> tuple:
        """
        🚀 O(1) SMART POSITION LOOKUP với intelligent caching

        Returns:
            tuple: (row, col) or (-1, -1) if no position available
        """
        # 🚀 REBUILD CACHE if dirty
        if self._dirty:
            self._rebuildCache()

        # 🚀 O(1) SEQUENTIAL MODE: Fast path for common pattern
        if self._sequential_mode and item_cols == 1 and item_rows == 1:
            if self._next_sequential < len(self._available_slots):
                pos = self._available_slots[self._next_sequential]
                self._next_sequential += 1
                self._cache_hits += 1
                self._last_position = pos
                return pos

        # 🚀 O(k) SEARCH MODE: Find suitable position for multi-cell items
        for i in range(self._next_sequential, len(self._available_slots)):
            row, col = self._available_slots[i]
            if self._canPlaceItemAt(row, col, item_cols, item_rows):
                self._next_sequential = i + 1
                self._cache_hits += 1
                self._last_position = (row, col)
                return (row, col)

        # 🚀 NO POSITION FOUND
        self._cache_misses += 1
        return (-1, -1)

    def _rebuildCache(self):
        """
        🚀 O(k) INTELLIGENT CACHE REBUILD where k = empty slots (not total grid)
        """
        self._available_slots.clear()
        self._next_sequential = 0

        # 🚀 GENERATE AVAILABLE POSITIONS in left-to-right, top-to-bottom order
        for row in range(self._grid_rows):
            for col in range(self._grid_cols):
                if (row, col) not in self._occupied_positions:
                    self._available_slots.append((row, col))

        self._dirty = False

        # 🚀 PATTERN DETECTION: Check if we're in sequential mode
        if self._last_position != (-1, -1) and self._available_slots:
            # Check if last position + 1 is in available slots (sequential pattern)
            last_row, last_col = self._last_position
            next_expected = self._getNextSequentialPosition(last_row, last_col)
            self._sequential_mode = next_expected in self._available_slots[:5]  # Check first 5 positions
        else:
            self._sequential_mode = True  # Default to sequential mode

    def _getNextSequentialPosition(self, row: int, col: int) -> tuple:
        """Get next position in left-to-right, top-to-bottom order"""
        next_col = col + 1
        if next_col >= self._grid_cols:
            next_col = 0
            row += 1
        return (row, next_col)

    def _canPlaceItemAt(self, row: int, col: int, item_cols: int, item_rows: int) -> bool:
        """
        🚀 O(k) FAST COLLISION CHECK where k = cells to check

        ⚠️ IMPORTANT: This method is used by SmartPositionCache and must be consistent
        with the main _canPlaceItemAt method in ListGridItems
        """
        # Bounds check
        if row + item_rows > self._grid_rows or col + item_cols > self._grid_cols:
            return False

        # 🚀 MULTI-CELL AWARE: Check all cells the item would occupy
        for r in range(row, row + item_rows):
            for c in range(col, col + item_cols):
                if (r, c) in self._occupied_positions:
                    return False
        return True

    def invalidate(self):
        """Mark cache as dirty for next rebuild"""
        self._dirty = True


class ListGridItems(QAbstractListModel):
    """
    Grid Items Collection - Quản lý collection của grid items với QAbstractListModel

    Chức năng chính:
    - Quản lý collection grid items với row/col coordinates
    - Tìm vị trí trống theo thuật toán từ trái qua phải, từ trên xuống dưới
    - Hỗ trợ multi-cell items với collision detection O(1)
    - Auto grid expansion theo progression: 1x1 -> 2x1 -> 2x2 -> 3x2 -> 3x3...
    - Swap operations với size exchange
    - QML integration thông qua QAbstractListModel
    """
    ObjectRole = Qt.UserRole + 1
    itemCountChanged = Signal()

    def __init__(self, parent=None, grid_model=None):
        """
        Khởi tạo ListGridItems collection

        Args:
            parent: QObject parent
            grid_model: Reference đến GridModel để access grid properties
        """
        super().__init__(parent)
        # Dictionary lưu trữ items theo (row, col) coordinates
        self._items: Dict[tuple, Any] = {}
        # Ordered list của (row, col) cho QAbstractListModel
        self._row_col_list: List[tuple] = []
        # Reference đến GridModel
        self._grid_model = grid_model

        # 🚀 SMART POSITION CACHING: Advanced caching với pre-computation
        self._smart_cache = SmartPositionCache()

        # ⚡ BATCH MODE: Tắt signals individual khi xử lý hàng loạt
        # Kịch bản: Add 50 cameras cùng lúc → thay vì emit 50 signals riêng lẻ (chậm),
        # bật batch_mode → skip individual signals → chỉ emit 1 signal cuối cùng
        # Giảm UI update từ 50 lần xuống 1 lần → tăng tốc đáng kể
        self._batch_mode = False      # True = skip individual signals, False = emit normal

    @Property(int, notify=itemCountChanged)
    def itemCount(self):
        """Số lượng items hiện tại trong collection"""
        return len(self._row_col_list)

    def rowCount(self, parent=None):
        """QAbstractListModel interface - trả về số lượng rows"""
        return len(self._row_col_list)

    def data(self, index, role=Qt.DisplayRole):
        """
        QAbstractListModel interface - trả về data cho QML

        Args:
            index: QModelIndex
            role: Data role

        Returns:
            GridItem object nếu role là ObjectRole
        """
        if not index.isValid() or not (0 <= index.row() < len(self._row_col_list)):
            return None

        if role == self.ObjectRole:
            row_col = self._row_col_list[index.row()]
            return self._items.get(row_col)
        return None

    def roleNames(self):
        """QAbstractListModel interface - định nghĩa role names cho QML"""
        return {
            self.ObjectRole: b"model"
        }

    def addItem(self, gridItem):
        """
        Thêm GridItem vào collection

        Args:
            gridItem: GridItem object cần thêm

        Returns:
            bool: True nếu thêm thành công
        """
        if not gridItem:
            return False

        row_col = (gridItem.row, gridItem.col)

        # Kiểm tra vị trí đã tồn tại chưa
        if row_col in self._items:
            return False

        # Update QAbstractListModel
        self.beginInsertRows(QModelIndex(), len(self._row_col_list), len(self._row_col_list))
        self._items[row_col] = gridItem
        self._row_col_list.append(row_col)
        self.endInsertRows()

        # ✅ MULTI-CELL FIX: Update grid model counter with actual cells occupied
        if self._grid_model and hasattr(self._grid_model, '_incrementOccupiedCells'):
            cells_occupied = getattr(gridItem, 'cols_cell', 1) * getattr(gridItem, 'rows_cell', 1)
            self._grid_model._incrementOccupiedCells(cells_occupied)

        #DON'T invalidate cache on every add - only when grid changes
        # Cache will be invalidated only when grid expands or items are removed

        # ✅ O(1) OPTIMIZATION: Use incremental update for new items
        if self._grid_model:
            if hasattr(self._grid_model, 'updateOccupiedMapIncremental'):
                # New item: no old positions, only new positions
                self._grid_model.updateOccupiedMapIncremental(gridItem, [], list(gridItem.occupied_positions))
            elif hasattr(self._grid_model, 'updateOccupiedMap'):
                # Fallback to full rebuild
                self._grid_model.updateOccupiedMap()

        # ✅ BATCH OPTIMIZATION: Only emit signal if not in batch mode
        if not self._batch_mode:
            self.itemCountChanged.emit()
            # Gọi updateIsSingleGridItem nếu có
            if self._grid_model and hasattr(self._grid_model, '_updateIsSingleGridItem'):
                self._grid_model._updateIsSingleGridItem()

        return True





    def getItemAt(self, row: int, col: int):
        """
        Lấy GridItem tại vị trí (row, col) - O(1) operation

        Args:
            row: Hàng
            col: Cột

        Returns:
            GridItem hoặc None nếu không tìm thấy
        """
        return self._items.get((row, col))

    def getItemAtAnyPosition(self, row: int, col: int):
        """
        ✅ FIX MULTI-CELL: Lấy GridItem tại bất kỳ vị trí occupied nào (bao gồm cả non-primary positions)

        Khác với getItemAt() chỉ check primary position, method này sẽ tìm item
        ngay cả khi (row, col) là occupied position của multi-cell item.

        Args:
            row: Hàng
            col: Cột

        Returns:
            GridItem nếu tìm thấy item chiếm vị trí này, None nếu không
        """
        # Trước tiên check primary position (O(1))
        primary_item = self._items.get((row, col))
        if primary_item:
            return primary_item

        # Nếu không phải primary position, scan qua tất cả items để tìm item nào occupy vị trí này
        # Complexity: O(n) nhưng chỉ khi không phải primary position
        for item in self._items.values():
            # Check nếu item này occupy vị trí (row, col)
            if (item.row <= row < item.row + item.rows_cell and
                item.col <= col < item.col + item.cols_cell):
                return item

        return None

    def hasItemAt(self, row: int, col: int) -> bool:
        """
        Kiểm tra có item tại vị trí (row, col) không

        Args:
            row: Hàng
            col: Cột

        Returns:
            bool: True nếu có item tại vị trí đó
        """
        return (row, col) in self._items

    def removeItemAt(self, row: int, col: int):
        """
        ✅ TRUE O(1): Unified removal synchronized với add operations
        """
        row_col = (row, col)
        if row_col not in self._items:
            return False

        # ✅ O(1): Direct item retrieval and preparation
        item = self._items[row_col]

        try:
            index = self._row_col_list.index(row_col)
        except ValueError:
            return False

        # ✅ O(1): Batch-aware model operations
        if not self._batch_mode:
            self.beginRemoveRows(QModelIndex(), index, index)

        # ✅ SIMPLIFIED: Let Python GC handle object cleanup

        # ✅ O(1): Direct dictionary and list operations
        del self._items[row_col]
        self._row_col_list.remove(row_col)

        # ✅ MULTI-CELL FIX: Update grid model counter with actual cells occupied
        if self._grid_model and hasattr(self._grid_model, '_decrementOccupiedCells'):
            cells_occupied = getattr(item, 'cols_cell', 1) * getattr(item, 'rows_cell', 1)
            self._grid_model._decrementOccupiedCells(cells_occupied)

        if not self._batch_mode:
            self.endRemoveRows()

        # ✅ TRUE O(1): SYNCHRONIZED với add operations
        # DON'T invalidate cache per removal - only when needed
        # Cache will be invalidated only at end of batch or when grid changes
        # This matches the add operations pattern

        # ✅ TRUE O(1): NO grid updates per removal
        # Grid updates will be handled by batch operations only
        # This eliminates the O(n) bottleneck completely

        # ✅ TRUE O(1): Batch-aware signal emission (synchronized với add)
        if not self._batch_mode:
            self.itemCountChanged.emit()
            if self._grid_model and hasattr(self._grid_model, '_updateIsSingleGridItem'):
                self._grid_model._updateIsSingleGridItem()

        return True

    def removeMultipleItemsBatch(self, positions: list) -> int:
        """
        ✅ TRUE O(1): Batch removal với single cache update

        Args:
            positions: List of (row, col) tuples to remove

        Returns:
            int: Number of items successfully removed
        """
        if not positions:
            return 0

        # 🚀 BATCH MODE ACTIVATION: Tối ưu cho bulk operations
        # Kịch bản: User chọn 20 cameras và delete all → thay vì:
        # - 20 lần beginRemoveRows/endRemoveRows (chậm)
        # - 20 lần itemCountChanged.emit() (lag UI)
        # - 20 lần cache invalidation (không cần thiết)
        # → Bật batch_mode → 1 lần beginRemoveRows, 1 lần endRemoveRows, 1 lần emit signal
        self._batch_mode = True
        removed_count = 0

        # ✅ COLLECT ITEMS: Validate positions first
        items_to_remove = []
        for row, col in positions:
            row_col = (row, col)
            if row_col in self._items:
                item = self._items[row_col]
                items_to_remove.append((row_col, item))

        if not items_to_remove:
            self._batch_mode = False
            return 0

        # Sort items by their index in _row_col_list (descending order to avoid index shifting)
        items_with_indices = []
        for row_col, item in items_to_remove:
            try:
                index = self._row_col_list.index(row_col)
                items_with_indices.append((index, row_col, item))
            except ValueError:
                continue

        # Sort by index in descending order (remove from end to beginning)
        items_with_indices.sort(key=lambda x: x[0], reverse=True)

        # Remove items one by one with correct model signals
        for index, row_col, item in items_with_indices:
            self.beginRemoveRows(QModelIndex(), index, index)

            # Remove from collections
            del self._items[row_col]
            self._row_col_list.remove(row_col)
            removed_count += 1

            self.endRemoveRows()

        self._batch_mode = False

        # ✅ MULTI-CELL: Update grid model counter with actual cells freed
        if removed_count > 0 and self._grid_model and hasattr(self._grid_model, '_decrementOccupiedCells'):
            total_cells_freed = 0
            for row_col, item in items_to_remove:
                cells_occupied = getattr(item, 'cols_cell', 1) * getattr(item, 'rows_cell', 1)
                total_cells_freed += cells_occupied
            self._grid_model._decrementOccupiedCells(total_cells_freed)

        # ✅ SINGLE O(1) OPERATIONS: Only at end of batch
        if removed_count > 0:
            self._invalidatePositionCache()  # Single cache invalidation
            self.itemCountChanged.emit()     # Single signal emission
            if self._grid_model and hasattr(self._grid_model, '_updateIsSingleGridItem'):
                self._grid_model._updateIsSingleGridItem()

        # ✅ TRUE O(1): NO grid updates per removal
        # Grid updates will be handled by batch operations only
        # This eliminates the O(n) bottleneck completely

        # ✅ TRUE O(1): Batch-aware signal emission (synchronized với add)
        if not self._batch_mode:
            self.itemCountChanged.emit()
            if self._grid_model and hasattr(self._grid_model, '_updateIsSingleGridItem'):
                self._grid_model._updateIsSingleGridItem()

        return removed_count

    def moveItemTo(self, from_row: int, from_col: int, to_row: int, to_col: int):
        """
        Move item with proper multi-cell collision detection

        Args:
            from_row: Source row
            from_col: Source column
            to_row: Target row
            to_col: Target column

        Returns:
            bool: True if move was successful
        """
        from_row_col = (from_row, from_col)
        to_row_col = (to_row, to_col)

        # Check if source item exists
        if from_row_col not in self._items:
            return False

        item = self._items[from_row_col]
        item_cols = getattr(item, 'cols_cell', 1)
        item_rows = getattr(item, 'rows_cell', 1)

        # Get current grid dimensions
        current_cols = getattr(self._grid_model, 'columns', 1) if self._grid_model else 1
        current_rows = getattr(self._grid_model, 'rows', 1) if self._grid_model else 1

        # Use proper multi-cell collision detection
        # Temporarily remove source item from collision check
        temp_item = self._items.pop(from_row_col)

        # Check if target position is valid (with source item excluded)
        can_place = self._canPlaceItemAt(to_row, to_col, item_cols, item_rows, current_cols, current_rows)

        # Restore source item
        self._items[from_row_col] = temp_item

        if not can_place:
            return False

        # Perform the move
        item.row = to_row
        item.col = to_col
        del self._items[from_row_col]
        self._items[to_row_col] = item

        # Update model indices
        old_index = self._row_col_list.index(from_row_col)
        self._row_col_list[old_index] = to_row_col
        moved_index = self._row_col_list.index(to_row_col)

        # Emit change signal
        self.dataChanged.emit(self.createIndex(moved_index, 0), self.createIndex(moved_index, 0), [self.ObjectRole])

        # ✅ MOVE: Trigger resize completion to update stream switching after move
        if hasattr(item, 'resizeCompleted'):
            # Use current width/height since position changed but size might be same
            current_width = getattr(item, 'width', 0)
            current_height = getattr(item, 'height', 0)
            item.resizeCompleted.emit(current_width, current_height)

        #Invalidate position cache after move
        self._invalidatePositionCache()

        return True

    def swapItemsRowCol(self, row1: int, col1: int, row2: int, col2: int):
        try:
            row_col1 = (row1, col1)
            row_col2 = (row2, col2)
            if row_col1 == row_col2:
                return True
            item1 = None
            item2 = None
            if self._grid_model and hasattr(self._grid_model, 'getItemFromRowCol'):
                item1 = self._grid_model.getItemFromRowCol(row1, col1)
                item2 = self._grid_model.getItemFromRowCol(row2, col2)
            if not item1:
                item1 = self._items.get(row_col1)
            if not item2:
                item2 = self._items.get(row_col2)
            if not item1 or not item2:
                return False
            width1 = item1.width
            height1 = item1.height
            cols1 = item1.cols_cell
            rows1 = item1.rows_cell
            primary_row1 = item1.row
            primary_col1 = item1.col
            primary_row2 = item2.row
            primary_col2 = item2.col
            width2 = item2.width
            height2 = item2.height
            cols2 = item2.cols_cell
            rows2 = item2.rows_cell
            item1.cols_cell = cols2
            item1.rows_cell = rows2
            item2.cols_cell = cols1
            item2.rows_cell = rows1
            item1.row = primary_row2
            item1.col = primary_col2
            item2.row = primary_row1
            item2.col = primary_col1
            item1.width = width2
            item1.height = height2
            item2.width = width1
            item2.height = height1
            primary_row_col1 = (primary_row1, primary_col1)
            primary_row_col2 = (primary_row2, primary_col2)
            self._items[primary_row_col1] = item2
            self._items[primary_row_col2] = item1
            idx1 = self._row_col_list.index(primary_row_col1)
            idx2 = self._row_col_list.index(primary_row_col2)
            self._row_col_list[idx1] = primary_row_col2
            self._row_col_list[idx2] = primary_row_col1
            self.dataChanged.emit(self.createIndex(idx1, 0), self.createIndex(idx1, 0), [self.ObjectRole])
            self.dataChanged.emit(self.createIndex(idx2, 0), self.createIndex(idx2, 0), [self.ObjectRole])

            # ✅ SWAP: Trigger resize completion for both items to update stream switching
            # Use current width/height after swap (items may have different sizes at new positions)
            if hasattr(item1, 'resizeCompleted'):
                current_width1 = getattr(item1, 'width', width2)  # item1 now has item2's old size
                current_height1 = getattr(item1, 'height', height2)
                item1.resizeCompleted.emit(current_width1, current_height1)
            if hasattr(item2, 'resizeCompleted'):
                current_width2 = getattr(item2, 'width', width1)  # item2 now has item1's old size
                current_height2 = getattr(item2, 'height', height1)
                item2.resizeCompleted.emit(current_width2, current_height2)

            return True
        except Exception as e:
            logger.error(f"Error in swap: {e}")
            return False





    def getAllItems(self) -> List[Any]:
        """
        ⚠️ DEPRECATED: Use getItemsDict() for better performance
        This method creates a list copy which is inefficient for large collections
        """
        return list(self._items.values())

    def getAllRowCols(self) -> List[tuple]:
        return self._row_col_list.copy()

    def getItemsDict(self) -> Dict[tuple, Any]:
        """
        ✅ OPTIMIZED: Get direct access to items dict for efficient iteration
        Use this instead of getAllItems() when you need to iterate over items

        Returns:
            Dict[tuple, Any]: Direct reference to internal items dict
        """
        return self._items



    def clear(self):
        if not self._items:
            return
        self.beginResetModel()
        self._items.clear()
        self._row_col_list.clear()
        self.endResetModel()

        #Invalidate position cache when clearing
        self._invalidatePositionCache()

        self.itemCountChanged.emit()
        if self._grid_model and hasattr(self._grid_model, '_updateIsSingleGridItem'):
            self._grid_model._updateIsSingleGridItem()





    def findNextAvailableRowCol(self, item_cols: int = 1, item_rows: int = 1, skip_validation: bool = False) -> tuple:
        """
        ✅ SIMPLIFIED: Pure SmartPositionCache approach - no legacy fallback

        ⚡ PERFORMANCE STRATEGY:
        1. Update SmartPositionCache with current grid state
        2. Use SmartPositionCache for O(1) sequential, O(k) sparse lookups
        3. Expand grid if no position found

        Args:
            item_cols: Số cột item chiếm (1x1, 2x1, 2x2...)
            item_rows: Số hàng item chiếm
            skip_validation: Skip validation (unused - legacy parameter)

        Returns:
            tuple: (row, col) - Vị trí available đầu tiên tìm được
        """
        current_cols = 1
        current_rows = 1
        if self._grid_model:
            current_cols = getattr(self._grid_model, 'columns', 1)
            current_rows = getattr(self._grid_model, 'rows', 1)

        #Update SmartPositionCache with current state
        self._smart_cache.updateGridDimensions(current_cols, current_rows)

        # ✅ BUILD OCCUPIED POSITIONS SET for O(1) lookups
        occupied_positions = set()
        for item in self._items.values():
            # Add all positions occupied by this item
            for r in range(item.row, item.row + getattr(item, 'rows_cell', 1)):
                for c in range(item.col, item.col + getattr(item, 'cols_cell', 1)):
                    occupied_positions.add((r, c))

        self._smart_cache.updateOccupiedPositions(occupied_positions)

        #Use SmartPositionCache for position finding
        smart_result = self._smart_cache.getNextPosition(item_cols, item_rows)
        if smart_result != (-1, -1):
            return smart_result

        #Grid is full - expand and find position
        result = self._expandGridAndFindPosition(item_cols, item_rows, current_cols, current_rows)

        # ✅ GRID LIMITS: Handle case where no position found
        if result is None:
            return (-1, -1)

        return result

    def _invalidatePositionCache(self):
        """
        ✅ SIMPLIFIED: Invalidate only SmartPositionCache
        """
        self._smart_cache.invalidate()

    def _canPlaceItemAt(self, row: int, col: int, item_cols: int, item_rows: int, grid_cols: int, grid_rows: int) -> bool:
        """
        🚀 FIXED: Consistent collision detection với SmartPositionCache

        ⚡ PERFORMANCE OPTIMIZATIONS:
        1. Early bounds check (O(1))
        2. Use pre-built occupied_positions set (O(k) where k = cells to check)
        3. Consistent logic với SmartPositionCache
        4. No iteration through all existing items

        Args:
            row: Hàng bắt đầu
            col: Cột bắt đầu
            item_cols: Số cột item chiếm
            item_rows: Số hàng item chiếm
            grid_cols: Tổng số cột grid
            grid_rows: Tổng số hàng grid

        Returns:
            bool: True nếu có thể đặt item
        """
        # 🚀 O(1) BOUNDS CHECK: Early termination
        if row + item_rows > grid_rows or col + item_cols > grid_cols:
            return False

        # 🚀 BUILD OCCUPIED POSITIONS SET: Same logic as SmartPositionCache
        occupied_positions = set()
        for item in self._items.values():
            # Add all positions occupied by this item (including multi-cell)
            for r in range(item.row, item.row + getattr(item, 'rows_cell', 1)):
                for c in range(item.col, item.col + getattr(item, 'cols_cell', 1)):
                    occupied_positions.add((r, c))

        # 🚀 O(k) MULTI-CELL AWARE: Check all cells the new item would occupy
        for r in range(row, row + item_rows):
            for c in range(col, col + item_cols):
                if (r, c) in occupied_positions:
                    return False  # Position occupied by existing item

        return True

    def _canPlaceItemAtWithCache(self, row: int, col: int, item_cols: int, item_rows: int,
                                grid_cols: int, grid_rows: int, occupied_positions: set) -> bool:
        """
        🚀 OPTIMIZED: Fast collision detection using pre-built occupied_positions set

        Args:
            row: Hàng bắt đầu
            col: Cột bắt đầu
            item_cols: Số cột item chiếm
            item_rows: Số hàng item chiếm
            grid_cols: Tổng số cột grid
            grid_rows: Tổng số hàng grid
            occupied_positions: Pre-built set of occupied positions

        Returns:
            bool: True nếu có thể đặt item
        """
        # 🚀 O(1) BOUNDS CHECK: Early termination
        if row + item_rows > grid_rows or col + item_cols > grid_cols:
            return False

        # 🚀 O(k) MULTI-CELL AWARE: Check all cells the new item would occupy
        for r in range(row, row + item_rows):
            for c in range(col, col + item_cols):
                if (r, c) in occupied_positions:
                    return False  # Position occupied by existing item

        return True



    def _expandGridAndFindPosition(self, item_cols: int, item_rows: int, current_cols: int, current_rows: int) -> tuple:
        """
        ✅ GRID LIMITS: Expand grid and find position with 12x12 maximum enforcement
        """
        from src.common.qml.models.grid_model import GridConstants

        # ✅ GRID LIMITS: Check if already at maximum
        if current_cols >= GridConstants.MAX_COLUMNS and current_rows >= GridConstants.MAX_ROWS:
            # Return position in remaining empty cells if any (silent check)
            return self._findPositionInCurrentGrid(item_cols, item_rows, current_cols, current_rows)

        # Get next grid size in progression
        next_cols, next_rows = self._getNextGridSizeInProgression(current_cols, current_rows)

        # Ensure grid is large enough for the item (but respect limits)
        while (next_cols < item_cols or next_rows < item_rows) and (next_cols < GridConstants.MAX_COLUMNS or next_rows < GridConstants.MAX_ROWS):
            next_cols, next_rows = self._getNextGridSizeInProgression(next_cols, next_rows)

        # ✅ CRITICAL: Actually expand the grid before finding position
        if self._grid_model:
            self._grid_model._batchUpdateGridDimensions(next_cols, next_rows)
            # ✅ CRITICAL: Invalidate cache after grid expansion
            self._invalidatePositionCache()

        # Find position in the expanded grid
        # 🚀 BUILD OCCUPIED POSITIONS for consistent collision detection
        occupied_positions = set()
        for item in self._items.values():
            for r in range(item.row, item.row + getattr(item, 'rows_cell', 1)):
                for c in range(item.col, item.col + getattr(item, 'cols_cell', 1)):
                    occupied_positions.add((r, c))

        # Priority 1: New columns (if grid expanded horizontally)
        if next_cols > current_cols:
            for row in range(next_rows):
                for col in range(current_cols, next_cols):
                    if self._canPlaceItemAtWithCache(row, col, item_cols, item_rows, next_cols, next_rows, occupied_positions):
                        return (row, col)

        # Priority 2: New rows (if grid expanded vertically)
        if next_rows > current_rows:
            for row in range(current_rows, next_rows):
                for col in range(current_cols):
                    if self._canPlaceItemAtWithCache(row, col, item_cols, item_rows, next_cols, next_rows, occupied_positions):
                        return (row, col)

        # Priority 3: Scan entire expanded grid (fallback)
        for row in range(next_rows):
            for col in range(next_cols):
                if self._canPlaceItemAtWithCache(row, col, item_cols, item_rows, next_cols, next_rows, occupied_positions):
                    return (row, col)

        # Fallback position
        return (max(0, next_rows - item_rows), max(0, next_cols - item_cols))

    def _findPositionInCurrentGrid(self, item_cols: int, item_rows: int, grid_cols: int, grid_rows: int) -> tuple:
        """
        🚀 OPTIMIZED: Find position in current grid without expansion using cached collision detection

        Args:
            item_cols: Item width
            item_rows: Item height
            grid_cols: Current grid columns
            grid_rows: Current grid rows

        Returns:
            tuple: (row, col) position or None if no space
        """
        # 🚀 BUILD OCCUPIED POSITIONS for consistent collision detection
        occupied_positions = set()
        for item in self._items.values():
            for r in range(item.row, item.row + getattr(item, 'rows_cell', 1)):
                for c in range(item.col, item.col + getattr(item, 'cols_cell', 1)):
                    occupied_positions.add((r, c))

        # Scan entire current grid for available position
        for row in range(grid_rows):
            for col in range(grid_cols):
                if self._canPlaceItemAtWithCache(row, col, item_cols, item_rows, grid_cols, grid_rows, occupied_positions):
                    return (row, col)

        # ✅ GRID LIMITS: No space available - return None to indicate failure (silent)
        return None

    def _getNextGridSizeInProgression(self, current_cols: int, current_rows: int) -> tuple:
        """
        ✅ GRID LIMITS: Correct grid progression with 12x12 maximum enforcement

        Pattern: Cột → Hàng → Cột → Hàng (tuần tự)
        1x1 → 2x1 (+cột) → 2x2 (+hàng) → 3x2 (+cột) → 3x3 (+hàng) → 4x3 (+cột) → 4x4 (+hàng)
        """
        from src.common.qml.models.grid_model import GridConstants

        # ✅ GRID LIMITS: Check if already at maximum
        if current_cols >= GridConstants.MAX_COLUMNS and current_rows >= GridConstants.MAX_ROWS:
            logger.warning(f"🚫 [GRID_LIMIT] Cannot expand beyond {GridConstants.MAX_COLUMNS}x{GridConstants.MAX_ROWS}")
            return (current_cols, current_rows)  # Stay at maximum

        # ✅ CORRECT LOGIC: Alternating column-row expansion with limits
        if current_cols == current_rows:
            # Square grid → Add column (make rectangular)
            next_cols = min(current_cols + 1, GridConstants.MAX_COLUMNS)
            return (next_cols, current_rows)
        elif current_cols > current_rows:
            # More columns than rows → Add row (make square or closer to square)
            next_rows = min(current_rows + 1, GridConstants.MAX_ROWS)
            return (current_cols, next_rows)
        else:
            # More rows than columns → Add column (make square or closer to square)
            next_cols = min(current_cols + 1, GridConstants.MAX_COLUMNS)
            return (next_cols, current_rows)

        # Note: This logic naturally creates the progression:
        # 1x1 → 2x1 (cols > rows, so next will add row)
        # 2x1 → 2x2 (cols == rows, so next will add col)
        # 2x2 → 3x2 (cols > rows, so next will add row)
        # 3x2 → 3x3 (cols == rows, so next will add col)
        # 3x3 → 4x3 (cols > rows, so next will add row)
        # 4x3 → 4x4 (cols == rows, so next will add col)
        # And so on...



    def addCameraItem(self, camera_model, item_cols: int = 1, item_rows: int = 1):
        """
        Correct logic - only expand when NO position found in current grid + detailed logging

        Args:
            camera_model: Camera model object
            item_cols: Number of columns item occupies (default: 1)
            item_rows: Number of rows item occupies (default: 1)

        Returns:
            bool: True if camera added successfully
        """
        try:
            # ✅ GRID LIMITS: findNextAvailableRowCol handles expansion internally
            # Only expand if no position found in current grid
            row, col = self.findNextAvailableRowCol(item_cols, item_rows)

            # ✅ GRID LIMITS: Check if position is valid
            if row == -1 or col == -1:
                logger.warning(f"🚫 [GRID_LIMIT] Cannot add camera - grid full at maximum capacity")
                return False

            # ✅ ORIGINAL: Create camera grid item
            camera_item = CameraGridItem()
            camera_item.row = row
            camera_item.col = col
            camera_item.cols_cell = item_cols
            camera_item.rows_cell = item_rows
            camera_item.cameraModel = camera_model
            camera_item.isNewlyAdded = True  # ✅ NEW: Set flag for animation
            # ✅ ORIGINAL: Add to collection (NO post-expansion logic)
            return self.addItem(camera_item)

        except Exception as e:
            camera_id = camera_model.get_property('id') if camera_model else 'Unknown'
            logger.error(f"Error adding camera item {camera_id}: {e}")
            return False

    def addItems(self, models: list) -> int:
        """
        🚀 SMART BATCHED ADDITION: Intelligent batch processing với predictive optimization

        ⚡ PERFORMANCE FEATURES:
        1. Batch detection (single vs multiple items)
        2. Predictive grid sizing
        3. Bulk position allocation
        4. Single UI update
        5. Smart cache management

        Args:
            models: List of camera models to add

        Returns:
            int: Number of cameras successfully added
        """
        if not models:
            return 0

        batch_size = len(models)
        current_cols = getattr(self._grid_model, 'columns', 1) if self._grid_model else 1
        current_rows = getattr(self._grid_model, 'rows', 1) if self._grid_model else 1

        # 🚀 BATCH DETECTION: Different strategies for single vs batch
        is_batch_operation = batch_size > 1

        if is_batch_operation:
            # 🚀 PREDICTIVE SIZING: Pre-expand grid for batch
            required_grid_size = self._calculateRequiredGridSizeForBatch(batch_size)
            if required_grid_size and self._grid_model:
                if (required_grid_size[0] > current_cols or required_grid_size[1] > current_rows):
                    self._grid_model.resizeGrid(required_grid_size[0], required_grid_size[1])
                    # 🚀 SMART CACHE: Update cache with new grid dimensions
                    self._smart_cache.updateGridDimensions(required_grid_size[0], required_grid_size[1])
                    self._invalidatePositionCache()

        # 🚀 BATCH MODE: Optimize for bulk operations
        self._batch_mode = True
        added_count = 0
        total_cells_added = 0

        # 🚀 SINGLE MODEL SIGNAL: Batch QAbstractListModel updates
        start_index = len(self._row_col_list)
        self.beginInsertRows(QModelIndex(), start_index, start_index + len(models) - 1)

        # 🚀 BULK PROCESSING: Add all items with minimal overhead
        for model in models:
            if self._addItemSilent(model):
                added_count += 1
                # Count cells for real-time counter update
                total_cells_added += 1  # Assuming 1x1 items for now

        self.endInsertRows()  # Single model signal
        self._batch_mode = False

        # 🚀 REAL-TIME COUNTER: Single update for all added cells
        if self._grid_model and total_cells_added > 0:
            if hasattr(self._grid_model, '_incrementOccupiedCells'):
                self._grid_model._incrementOccupiedCells(total_cells_added)

        # 🚀 SINGLE CUSTOM SIGNAL: Only emit once
        if added_count > 0:
            self.itemCountChanged.emit()
            if self._grid_model and hasattr(self._grid_model, '_updateIsSingleGridItem'):
                self._grid_model._updateIsSingleGridItem()

        # 🚀 SMART CACHE: Update cache with final state
        if added_count > 0:
            self._smart_cache.invalidate()  # Mark for rebuild on next access

        # 🚀 SINGLE SYNC: Update GridModel once (deprecated but kept for compatibility)
        if self._grid_model:
            if hasattr(self._grid_model, 'updateOccupiedMap'):
                self._grid_model.updateOccupiedMap()

        return added_count

    def _calculateRequiredGridSizeForBatch(self, batch_size: int) -> tuple:
        """
        Calculate required grid size - only expand when actually needed

        Args:
            batch_size: Number of items to add

        Returns:
            tuple: (cols, rows) required grid size or None if no expansion needed
        """
        if not self._grid_model:
            return None

        current_cols = getattr(self._grid_model, 'columns', 1)
        current_rows = getattr(self._grid_model, 'rows', 1)
        current_capacity = current_cols * current_rows

        # Calculate current occupied cells
        occupied_cells = len(self._items)
        available_cells = current_capacity - occupied_cells

        # Check if current grid has enough space
        if available_cells >= batch_size:
            return None

        # Only expand if actually needed
        cells_needed = occupied_cells + batch_size

        # ✅ CONSERVATIVE: Add minimal buffer only for multi-cell items
        multi_cell_buffer = 0
        for item in self._items.values():
            item_size = getattr(item, 'cols_cell', 1) * getattr(item, 'rows_cell', 1)
            if item_size > 1:
                multi_cell_buffer += 1  # Add 1 cell buffer per multi-cell item

        total_cells_needed = cells_needed + multi_cell_buffer

        # ✅ GRID LIMITS: Use GridConstants for progression
        from src.common.qml.models.grid_model import GridConstants

        for cols, rows in GridConstants.GRID_PROGRESSION:
            capacity = cols * rows
            if capacity >= total_cells_needed:
                return (cols, rows)

        # ✅ GRID LIMITS: Fallback to maximum size from constants
        return (GridConstants.MAX_COLUMNS, GridConstants.MAX_ROWS)

    def _createNewItem(self, itemType):
        """
        ✅ SIMPLIFIED: Create new item directly without pooling

        Args:
            itemType: Type of item to create ("Camera", "DigitalMap", "FloorMap")

        Returns:
            New grid item instance
        """
        if itemType == "Camera":
            return CameraGridItem()
        elif itemType == "DigitalMap":
            return DigitalMapGridItem()
        elif itemType == "FloorMap":
            return FloorMapGridItem()
        elif itemType == "Event":
            return EventGridItem()
        else:
            # Default to camera item
            return CameraGridItem()



    def _addItemSilent(self, model) -> bool:
        """
       Add camera without emitting signals (for batch operations)

        Args:
            model: Camera model to add

        Returns:
            bool: True if successfully added
        """
        try:
            # ✅ O(1) PERFORMANCE: Use optimized method with validation skip for batch operations
            row, col = self.findNextAvailableRowCol(1, 1, skip_validation=True)

            # ✅ GRID LIMITS: Check if position is valid (should not happen with early validation)
            if row == -1 or col == -1:
                logger.warning(f"🚫 [GRID_LIMIT] Cannot add camera - grid full at maximum capacity")
                return False

            #Grid expansion should be handled by pre-sizing in addCameraItems()
            # No individual expansion needed here
            logger.info(f"_addItemSilent {model}")
            itemType = "Camera"
            if isinstance(model,MapModel):
                itemType = "DigitalMap"
            elif isinstance(model,FloorModel):
                itemType = "FloorMap"
            elif isinstance(model,EventModel):
                itemType = "Event"
            # ✅ SIMPLIFIED: Create new item directly
            item = self._createNewItem(itemType)
            item.row = row
            item.col = col
            item.cols_cell = 1
            item.rows_cell = 1
            item.isNewlyAdded = True  # ✅ NEW: Set flag for animation

            # Ensure default selection state for new items
            if hasattr(item, 'selected'):
                item.selected = False
            if isinstance(item,CameraGridItem):
                item.cameraModel = model
            elif isinstance(item,DigitalMapGridItem):
                item.mapModel = model
            elif isinstance(item,FloorMapGridItem):
                item.floorModel = model
            elif isinstance(item,EventGridItem):
                item.eventModel = model
            # ✅ SILENT ADD: No signals during batch
            row_col = (row, col)
            if row_col in self._items:
                return False

            self._items[row_col] = item
            self._row_col_list.append(row_col)

            # 🚀 O(1) OPTIMIZATION: Skip cache invalidation during batch
            # Cache will be invalidated once after batch completion
            # This prevents O(n) cache rebuilds during batch operations

            return True

        except Exception as e:
            camera_id = model.get_property('id') if model else 'Unknown'
            logger.error(f"Error adding camera item {camera_id}: {e}")
            return False

    def calculateOptimalGridSize(self, item_count: int, consider_multi_cell: bool = True) -> tuple:
        if item_count <= 0:
            return (1, 1)

        # ✅ GRID LIMITS: Use GridConstants for progression
        from src.common.qml.models.grid_model import GridConstants
        effective_item_count = item_count
        if consider_multi_cell and self._items:
            total_cells_used = 0
            for item in self._items.values():
                item_cells = getattr(item, 'cols_cell', 1) * getattr(item, 'rows_cell', 1)
                total_cells_used += item_cells
            if total_cells_used > len(self._items):
                effective_item_count = max(item_count, int(total_cells_used * 1.2))
        for cols, rows in GridConstants.GRID_PROGRESSION:
            capacity = cols * rows
            if capacity >= effective_item_count:
                return (cols, rows)
        return (GridConstants.MAX_COLUMNS, GridConstants.MAX_ROWS)

    def getCurrentGridSize(self) -> tuple:
        if self._grid_model:
            cols = getattr(self._grid_model, 'columns', 1)
            rows = getattr(self._grid_model, 'rows', 1)
            return (cols, rows)
        return (1, 1)

    def clearAllSelection(self):
        pass

    def insertItemAt(self, position, item):
        self.beginInsertRows(QModelIndex(), len(self._row_col_list), len(self._row_col_list))
        self._items[position] = item
        self._row_col_list.append(position)
        self.endInsertRows()
