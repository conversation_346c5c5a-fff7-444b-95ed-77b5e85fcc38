from PySide6.QtCore import Qt
from PySide6.QtWidgets import QSizePolicy, QWidget, QVBoxLayout, QHBoxLayout
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtGui import QStandardItemModel, QStandardItem
from PySide6.QtCore import QUrl, QObject, Signal, Slot, Property
from src.common.controller.main_controller import main_controller
from src.common.threads.sub_thread import SubThread
from src.common.model.camera_model import CameraModel,camera_model_manager
from src.common.model.group_model import GroupModel, group_model_manager
import json
import ast
import re
from unidecode import unidecode
from src.common.widget.dialogs.base_dialog import NewBaseDialog
import logging
logger = logging.getLogger(__name__)

class MatrixModel(QObject):
    dataChanged = Signal()

    def __init__(self,data = None):
        super().__init__()
        if data is None:
            self._selected_cells = [[False] * 24 for _ in range(7)]
        else:
            self._selected_cells = data

    @Property('QVariantList', notify=dataChanged)
    def selectedCells(self):
        return self._selected_cells

    @Slot(int, int, bool)
    def setCell(self, day, hour, value):
        if 0 <= day < 7 and 0 <= hour < 24:
            self._selected_cells[day][hour] = value
            self.dataChanged.emit()

    @Slot(int, int, result=bool)
    def getCell(self, row, col):
        return self._selected_cells[row][col]

    @Slot(result='QVariantList')
    def getSelectedCells(self):
        return self._selected_cells
    
class ScheduleController(QObject):
    selectionChanged = Signal()
    enableRecordingChanged = Signal()
    matrixModelChanged = Signal()
    openDialogChanged = Signal(QObject)
    videoEncoderConfigurationsDataChanged = Signal()

    def __init__(self, camera_model:CameraModel = None):
        super().__init__()
        self.camera_model = camera_model
        self._matrixModel = MatrixModel()
        # self.selected_cells = [[False] * 24 for _ in range(7)]  # 7 days, 24 hours
        self.is_record_mode = True  # Default to "Record Always"
        self.dragging = False  # Track if mouse is dragging
        self.drag_mode = None  # Store selection state during drag
        self._fps_value = 10
        self._quality_index = 1  # ["Low", "Medium", "High", "Best"] ~ [0, 1, 2, 3]
        self._time_archive = 1
        self._time_unit_index = 0  # ["Days", "Months"] ~ [0, 1]
        self._list_quality_value = ["Lo", "Me", "Hi", "Be"]
        self._quality_text_value = self._list_quality_value[self._quality_index]
        self._enableRecording = False
        self.recordingScheduleData = None
        self._videoEncoderConfigurationsData = {}

    @Property(QObject,notify=matrixModelChanged)
    def matrixModel(self):
        return self._matrixModel
    
    @matrixModel.setter
    def matrixModel(self, value: QObject):
        if self._matrixModel != value:
            self._matrixModel = value
            self.matrixModelChanged.emit() 

    def setRecordingScheduleData(self,data):
        self.recordingScheduleData = data
        if self.recordingScheduleData['scheduleTime'] is not None:
            schedule_time_dict = ast.literal_eval(self.recordingScheduleData['scheduleTime'])
            self.recordingScheduleData['scheduleTime'] = schedule_time_dict
            temp = self.convertDataToList(self.recordingScheduleData['scheduleTime'])
            self.matrixModel = MatrixModel(data=temp)
            scheduleDelete = self.recordingScheduleData.get("scheduleDelete",1)
            scheduleDeleteUnit = self.recordingScheduleData.get("scheduleDeleteUnit","DAY")
            index = 0 if scheduleDeleteUnit == "DAY" else 1
            self.set_time_archive(scheduleDelete)
            self.set_time_unit_index(index)
            self.selectionChanged.emit()
        handleType = self.recordingScheduleData.get("handleType",None)
        self.enableRecording = True if handleType == "START" else False

    @Property(dict,notify=videoEncoderConfigurationsDataChanged)
    def videoEncoderConfigurationsData(self):
        return self._videoEncoderConfigurationsData
    
    @videoEncoderConfigurationsData.setter
    def videoEncoderConfigurationsData(self,data):
        self._videoEncoderConfigurationsData = data
        rateControl = self._videoEncoderConfigurationsData.get("RateControl",None)
        quality = self._videoEncoderConfigurationsData.get("Quality",None)
        if rateControl is not None:
            frameRateLimit = rateControl.get("FrameRateLimit",None)
            self.set_fps_value(frameRateLimit)
        if quality is not None:
            self.set_quality_index(quality - 1)
        self.videoEncoderConfigurationsDataChanged.emit()

    @Property(bool,notify=enableRecordingChanged)
    def enableRecording(self):
        return self._enableRecording
    
    @enableRecording.setter
    def enableRecording(self, value: bool):
        if self._enableRecording != value:
            self._enableRecording = value
            self.enableRecordingChanged.emit() 

    # @Slot(int, int, result=bool)
    # def isSelected(self, row, col):
    #     """ Returns whether a specific cell is selected. """
    #     return self.selected_cells[row][col]

    @Slot(bool)
    def setRecordMode(self, is_record):
        """Sets the selection mode based on the chosen button."""
        self.is_record_mode = is_record
        logger.debug(f'setRecordMode = {self.is_record_mode}')

    @Slot()
    def selectAll(self):
        for row in range(7):
            for col in range(24):
                # self.selected_cells[row][col] = self.is_record_mode
                self._matrixModel.setCell(row,col,self.is_record_mode)
        self.selectionChanged.emit()

    @Slot(int, int)
    def selectCell(self, row, col):
        """Toggles individual cell selection without affecting others."""
        # self.selected_cells[row][col] = self.is_record_mode
        self._matrixModel.setCell(row,col,self.is_record_mode)
        self.selectionChanged.emit()

    @Slot(int)
    def selectColumn(self, column_index):
        print(f"HanhLT: selectColumn  = {column_index}")
        """ Selects an entire column in grid_option (corresponding to grid_hour selection). """
        for row in range(7):
            # self.selected_cells[row][column_index] = self.is_record_mode
            self._matrixModel.setCell(row,column_index,self.is_record_mode)
        self.selectionChanged.emit()

    @Slot(int)
    def selectRow(self, row_index):
        """ Selects an entire row in grid_option (corresponding to grid_day selection). """
        for col in range(24):
            # self.selected_cells[row_index][col] = self.is_record_mode
            self._matrixModel.setCell(row_index,col,self.is_record_mode)
        self.selectionChanged.emit()

    @Slot()
    def startDrag(self):
        """Called when the user starts dragging."""
        self.dragging = True

    @Slot()
    def endDrag(self):
        """Called when the user releases the mouse after dragging."""
        self.dragging = False
        self.selectionChanged.emit()

    @Slot(QObject, "QVariant", "QVariant")
    def selectInRange(self, grid, start, end):
        """Selects cells within the dragged area."""
        if not self.dragging:
            return
        min_x, min_y = min(start.x(), end.x()), min(start.y(), end.y())
        max_x, max_y = max(start.x(), end.x()), max(start.y(), end.y())

        children = grid.childItems()  # 🔹 Get actual cell items
        if not children:
            print("No grid children found!")
            return

        for row in range(7):
            for col in range(24):
                index = row * 24 + col
                if index >= len(children):
                    continue  # Prevent index out of range

                item = children[index]  # Get grid cell item
                item_x, item_y = item.x(), item.y()  # No need to map anymore
                item_w, item_h = item.width(), item.height()

                # Debugging: Check grid cell positions
                # print(f"HanhLT: Checking row={row}, col={col}, item_x={item_x}, item_y={item_y}, "
                #     f"min_x={min_x}, max_x={max_x}, min_y={min_y}, max_y={max_y}")

                # Selection logic: If any part of the cell is inside the selection box
                if (item_x < max_x and item_x + item_w > min_x and
                    item_y < max_y and item_y + item_h > min_y):
                    # self.selected_cells[row][col] = self.is_record_mode
                    self._matrixModel.setCell(row,col,self.is_record_mode)
        # logger.debug(f'selectionChanged = {self.selected_cells}')
        self.selectionChanged.emit()

    # FPS Value
    def get_fps_value(self):
        return self._fps_value

    def set_fps_value(self, value):
        if self._fps_value != value:
            self._fps_value = value
            self.selectionChanged.emit()

    # Archive Time
    def get_time_archive(self):
        return self._time_archive

    def set_time_archive(self, value):
        if self._time_archive != value:
            self._time_archive = value
            self.selectionChanged.emit()  # Notify QML of changes

    # Quality Index
    def get_quality_index(self):
        return self._quality_index

    def set_quality_index(self, value):
        if self._quality_index != value:
            self._quality_index = value
            self._quality_text_value = self._list_quality_value[self._quality_index]
            self.selectionChanged.emit()

    # Time Unit Index
    def get_time_unit_index(self):
        return self._time_unit_index

    def set_time_unit_index(self, value):
        if self._time_unit_index != value:
            self._time_unit_index = value
            self.selectionChanged.emit()

    def get_quality_text_value(self):
        return self._quality_text_value
    
    def set_quality_text_value(self, value):
        if self._quality_text_value != value:
            self._quality_text_value = value
            self.selectionChanged.emit()

    fps_value = Property(int, get_fps_value, set_fps_value, notify=selectionChanged)
    time_archive = Property(int, get_time_archive, set_time_archive, notify=selectionChanged)
    quality_index = Property(int, get_quality_index, set_quality_index, notify=selectionChanged)
    time_unit_index = Property(int, get_time_unit_index, set_time_unit_index, notify=selectionChanged)
    quality_text_value = Property(str, get_quality_text_value, set_quality_text_value, notify=selectionChanged)

    # Get grid value
    @Slot()
    def getSelectedTimes(self):
        selected_times = {}
        days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

        for day_index in range(7):
            selected_hours = []
            for hour in range(24):
                # if self.selected_cells[day_index][hour]:
                if self._matrixModel.selectedCells[day_index,hour]:
                    selected_hours.append(hour)

            # Nhóm giờ liên tục lại
            if selected_hours:
                day_schedule = {}

                start = selected_hours[0]
                for i in range(1, len(selected_hours)):
                    if selected_hours[i] != selected_hours[i - 1] + 1:
                        # Khi có khoảng trống, kết thúc đoạn trước
                        end = selected_hours[i - 1] + 1
                        day_schedule[str(start)] = "start"
                        for h in range(start + 1, end):
                            day_schedule[str(h)] = "restart"
                        day_schedule[str(end)] = "stop"

                        # Bắt đầu đoạn tiếp theo
                        start = selected_hours[i]

                # Xử lý đoạn cuối cùng
                end = selected_hours[-1] + 1
                day_schedule[str(start)] = "start"
                for h in range(start + 1, end):
                    day_schedule[str(h)] = "restart"
                day_schedule[str(end)] = "stop"
                selected_times[days[day_index]] = day_schedule

        return selected_times
    
    def get_data(self):
        return {
            "scheduleTime": json.dumps(self.convertSelectedCells()),
            "cameraId": self.camera_model.get_property('id'),
            "scheduleDelete": self.time_archive,
            "scheduleDeleteUnit": "DAY" if self.time_unit_index == 0 else "MONTH",
            "handleType": "START" if self._enableRecording else "STOP"
        }
    
    def get_recording_schedule(self):
        logger.debug(f'get_recording_schedule {self.time_unit_index,self.time_archive}')
        if self.recordingScheduleData is None:
            data = {
                "scheduleTime": json.dumps(self.convertSelectedCells()),
                "cameraId": self.camera_model.get_property('id'),
                "scheduleDelete": self.time_archive,
                "scheduleDeleteUnit": "DAY" if self.time_unit_index == 0 else "MONTH",
                "handleType": "START" if self._enableRecording else "STOP"
            }
            main_controller.current_controller.create_recording_schedule(data=data)
        else:
            self.recordingScheduleData = {
                "id": self.recordingScheduleData.get("id",None),
                "scheduleTime": json.dumps(self.convertSelectedCells()),
                "cameraId": self.camera_model.get_property('id'),
                "scheduleDelete": self.time_archive,
                "scheduleDeleteUnit": "DAY" if self.time_unit_index == 0 else "MONTH",
                "handleType": "START" if self._enableRecording else "STOP"
            }
            
            update_recording_schedule = SubThread(parent=main_controller.list_parent["CameraScreen"],target=main_controller.current_controller.update_recording_schedule_by_patch,args=(self.recordingScheduleData,))
            update_recording_schedule.start()
        if len(self._videoEncoderConfigurationsData) > 0:
            if "RateControl" in self._videoEncoderConfigurationsData:
                if "FrameRateLimit" in self._videoEncoderConfigurationsData["RateControl"]:
                    self._videoEncoderConfigurationsData["RateControl"]["FrameRateLimit"] = self.fps_value
            if "Quality" in self._videoEncoderConfigurationsData:
                self._videoEncoderConfigurationsData["Quality"] = self.quality_index + 1
            data = {
                "cameraId": self.camera_model.get_property('id'),
                "index": 0,
                "Configuration": self._videoEncoderConfigurationsData
            }
            set_video_encoder_configurations = SubThread(parent=main_controller.list_parent["CameraScreen"],target=main_controller.current_controller.api_client.set_video_encoder_configurations,args=(data,))
            set_video_encoder_configurations.start()

    def convertSelectedCells(self):
        def get_day(row):
            if row == 0:
                return "Monday"
            elif row == 1:
                return "Tuesday"
            elif row == 2:
                return "Wednesday"
            elif row == 3:
                return "Thursday"
            elif row == 4:
                return "Friday"
            elif row == 5:
                return "Saturday"
            elif row == 6:
                return "Sunday"
        data = {}
        
        # convertedData = self.filterRecordSchedule(self._matrixModel.selectedCells)
        # logger.info(f'bbbbbbbbbbbbb = {convertedData}')
        # for row in range(len(convertedData)):
        #     day_key = get_day(row)
        #     hourData = {}
        #     for col in range(len(convertedData[row])):
        #         if convertedData[row][col] is not None:
        #             hourData[str(col)] = "restart" if convertedData[row][col] else "stop"
        #     data[day_key] = hourData
        # return data
        for row in range(len(self._matrixModel.selectedCells)):
            day_key = get_day(row)
            hourData = {}
            for col in range(len(self._matrixModel.selectedCells[row])):
                # if self._matrixModel.selectedCells[row][col] is not None:
                hourData[str(col)] = "restart" if self._matrixModel.selectedCells[row][col] else "stop"
            data[day_key] = hourData
        # logger.info(f'aaaaaaaaaaaaaaaa = {data}')
        return data
    
    def convertDataToList(self,input):
        days_order = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        result = []
        for day in days_order:
            day_schedule = input.get(day, {})
            row = []
            for hour in range(24):
                hour_str = str(hour)
                if hour_str in day_schedule:
                    row.append(day_schedule[hour_str] == "restart")
                else:
                    row.append(False)
            result.append(row)
        return result
    
    def filterRecordSchedule(self, input):
        result = []
        preserve_first_false = input[-1][-1] is True and input[0][0] is False

        # Xác định các chỉ số cần giữ vì là đầu list sau True ở cuối list trước
        preserve_false_heads = set()
        for i in range(len(input) - 1):
            if input[i][-1] is True and input[i + 1][0] is False:
                preserve_false_heads.add(i + 1)

        # Xử lý từng hàng
        for row_index, row in enumerate(input):
            new_row = []
            for i in range(len(row)):
                val = row[i]
                if val is True:
                    new_row.append(True)
                elif val is False:
                    # Điều kiện đặc biệt cho phần tử đầu tiên của list đầu tiên
                    if preserve_first_false and row_index == 0 and i == 0:
                        new_row.append(False)
                    # Điều kiện cho phần tử đầu tiên của list nếu list trước đó kết thúc bằng True
                    elif row_index in preserve_false_heads and i == 0:
                        new_row.append(False)
                    # Giữ False nếu đứng sau một True và phần sau không phải True nữa
                    elif i > 0 and row[i - 1] is True and (i + 1 == len(row) or row[i + 1] is not True):
                        new_row.append(False)
                    else:
                        new_row.append(None)
                else:
                    new_row.append(None)
            result.append(new_row)
        return result
      
    @Slot(str, result=str)
    def get_color_theme_by_key(self, key=None, alpha=1.0):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key, alpha)

    @Slot(str, result=str)
    def get_image_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Image", key)

class RecordingSchedule(QWidget):
    def __init__(self, parent=None,camera_model:CameraModel = None):
        super().__init__(parent)
        self.camera_model = camera_model
        self.load_ui()
        self.load_data()

    def load_ui(self):
        # create layout
        self.central_layout = QVBoxLayout()
        self.central_layout.setContentsMargins(0, 0, 0, 0)
        self.quick_widget = QQuickWidget()
        self.schedule_controller = ScheduleController(self.camera_model)
        self.schedule_controller.openDialogChanged.connect(self.openDialogChanged)
        self.quick_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)   
        self.quick_widget.engine().rootContext().setContextProperty("schedule_controller", self.schedule_controller)
        self.quick_widget.setSource(QUrl("qrc:src/common/qml/videoplayback/ScheduleUI.qml"))
        self.quick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)

        temp_widget = QWidget()
        temp_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        temp_layout = QVBoxLayout()
        temp_layout.setContentsMargins(0, 0, 0, 0)
        temp_layout.addWidget(self.quick_widget)
        temp_widget.setLayout(temp_layout)

        self.central_layout.addWidget(temp_widget)
        self.setLayout(self.central_layout)

    def load_data(self):
        self.getALLRecordSchedule()
        self.get_recording_schedule()
        self.get_video_encoder_configuration()

    def getALLRecordSchedule(self):
        subThread = SubThread(parent=self,target=main_controller.current_controller.get_recording_schedule)
        subThread.start()

    def get_recording_schedule(self):
        def callback(data):
            if data is not None:
                if len(data) > 0:
                    self.schedule_controller.setRecordingScheduleData(data[0])
        subThread = SubThread(parent=self,target=main_controller.current_controller.get_recording_schedule,args=(None,self.camera_model.get_property('id')),callback=callback)
        subThread.start()

    def get_video_encoder_configuration(self):
        def callback(response):
            logger.info(f'get_video_encoder_configuration = {response}')
            if response is not None:
                logger.info(f'get_video_encoder_configuration1 = {response}')
                data = response.get("data",[])
                if len(data)>0:
                    configuration = data[0].get("Configuration",None)
                    if configuration is not None:
                        configuration = configuration.get("Configuration",None)
                        # logger.debug(f'get_video_encoder_configuration = {configuration}')
                        self.schedule_controller.videoEncoderConfigurationsData = configuration
        subThread = SubThread(parent=self,target=main_controller.current_controller.api_client.get_video_encoder_configurations,args=(0,self.camera_model.get_property('id'),),callback=callback)
        subThread.start()

    @Slot()
    def getAllValue(self):
        times = self.schedule_controller.getSelectedTimes()

    def openDialogChanged(self, data):
        logger.debug(f'openDialogChanged = {data}')
        dialog = SelectCamerasDialog(schedule_controller = data)
        result = dialog.exec()
    def closeEvent(self, event):
        self.quick_widget.setSource(QUrl())  # Unload QML
        self.quick_widget.deleteLater()  # Delete widget safely
        event.accept()
class CustomTreeModel(QStandardItemModel):
    # Tín hiệu để phát đi khi checked thay đổi
    checkedChanged = Signal(int, bool)  # row, new_value

    def setData(self, index, value, role=Qt.EditRole):
        if role == Qt.UserRole + 4:
            logger.info(f"Checked changed at row {index.row()}: {value}")
            self.checkedChanged.emit(index.row(), value)
        return super().setData(index, value, role)
    
class SelectCamerasController(QObject):
    closeChanged = Signal()
    treeModelChanged = Signal()
    searchNotFoundChanged = Signal()
    def __init__(self):
        super().__init__()
        self.checkboxSaved = {}
        self._treeModel = self.create_tree_model()
        self._searchNotFound = False

    @Property(QObject,notify=treeModelChanged)
    def treeModel(self):
        return self._treeModel
    
    @treeModel.setter
    def treeModel(self, value: QObject):
        if self._treeModel != value:
            self._treeModel = value
            self.treeModelChanged.emit() 

    @Property(bool,notify=searchNotFoundChanged)
    def searchNotFound(self):
        return self._searchNotFound
    
    @searchNotFound.setter
    def searchNotFound(self, value: bool):
        if self._searchNotFound != value:
            self._searchNotFound= value
            self.searchNotFoundChanged.emit() 

    @Slot(str)
    def searchText(self,text):
        self._treeModel.clear()
        self.treeModel = self.create_tree_model()
        unaccented_text = unidecode(text)
        self.filter_recursive(self._treeModel.invisibleRootItem(), text=text, unaccented_text=unaccented_text)
        if self._treeModel.rowCount() == 0 or not self.has_visible_items(self._treeModel.invisibleRootItem()):
            self.searchNotFound = True
        else:
            self.searchNotFound = False
    def create_item(self,camera_id, name, iconCamera = "", checked=False,itemType = "Invalid"):
        item = QStandardItem()
        item.setData(name, Qt.DisplayRole)              # model.display
        item.setData(camera_id, Qt.UserRole + 1)
        item.setData(iconCamera, Qt.UserRole + 2)          # model.checked
        item.setData(checked, Qt.UserRole + 3)          # model.checked
        item.setData(itemType, Qt.UserRole + 4)          # model.checked
        item.setEditable(False)
        return item
    
    def create_tree_model(self):
        model = CustomTreeModel()
        model.setHorizontalHeaderLabels(["Name"])
        model.setItemRoleNames({
            Qt.DisplayRole: b"display",
            Qt.UserRole + 1: b"camera_id",
            Qt.UserRole + 2: b"iconCamera",
            Qt.UserRole + 3: b"checked",
            Qt.UserRole + 4: b"itemType"
        })

        for server_ip,listGroups in group_model_manager.group_list.items(): 
            parent = self.create_item(None,server_ip, checked = True)
            model.appendRow(parent)
            for id,groupModel in listGroups.items(): 
                groupModel:GroupModel
                groupItem = self.create_item(groupModel.get_property('id'),groupModel.get_property('name'), checked = True)
                temp = False
                for camera_id in groupModel.get_property("cameraIds"):
                    temp = True
                    cameraModel:CameraModel = camera_model_manager.get_camera_model(id = camera_id)
                    if cameraModel is not None:
                        checked = False
                        if cameraModel.get_property('id') in self.checkboxSaved:
                            checked = self.checkboxSaved[cameraModel.get_property('id')]
                        self.checkboxSaved[cameraModel.get_property('id')] = checked
                        iconCamera = CameraModel.get_qml_icon(cameraModel.state_merged)
                        cameraItem = self.create_item(cameraModel.get_property('id'),cameraModel.get_property('name'),iconCamera = iconCamera,checked = checked,itemType = "Camera")
                        groupItem.appendRow(cameraItem)
                if temp:
                    parent.appendRow(groupItem)
            listCameras = camera_model_manager.get_camera_list(server_ip=server_ip)
            for id,cameraModel in listCameras.items(): 
                cameraModel:CameraModel
                if cameraModel.get_property("cameraGroupIds") is None or len(cameraModel.get_property("cameraGroupIds")) == 0:
                    checked = False
                    if cameraModel.get_property('id') in self.checkboxSaved:
                        checked = self.checkboxSaved[cameraModel.get_property('id')]
                    self.checkboxSaved[cameraModel.get_property('id')] = checked
                    iconCamera = CameraModel.get_qml_icon(cameraModel.state_merged)
                    cameraItem = self.create_item(cameraModel.get_property('id'),cameraModel.get_property('name'),iconCamera = iconCamera,checked = checked,itemType = "Camera")
                    parent.appendRow(cameraItem)

        return model
    
    def filter_recursive(self, item: QStandardItem, text: str, unaccented_text: str):
        try:
            for row in range(item.rowCount() - 1, -1, -1):
                child_item = item.child(row)
                pattern: re.Pattern = re.compile(text, re.IGNORECASE | re.UNICODE)
                if (
                        pattern.search(child_item.text())
                        or unaccented_text.lower() in unidecode(child_item.text()).lower()
                ):
                    self.filter_recursive(child_item, text, unaccented_text)
                else:
                    self.filter_recursive(child_item, text, unaccented_text)
                    if not self.item_has_matching_child(child_item, text, unaccented_text):
                        item.removeRow(row)
        except Exception as e:
            logger.critical(f"filter_recursive: {e}")

    def item_has_matching_child(
            self, item: QStandardItem, text: str, unaccented_text: str
    ):
        for row in range(item.rowCount()):
            child_item = item.child(row)
            pattern: re.Pattern = re.compile(text, re.IGNORECASE | re.UNICODE)
            if (
                    pattern.search(child_item.text())
                    or unaccented_text.lower() in unidecode(child_item.text()).lower()
            ):
                return True
            elif self.item_has_matching_child(child_item, text, unaccented_text):
                return True
        return False
    
    def has_visible_items(self, item):
        if item.rowCount() == 0:
            return False
        for row in range(item.rowCount()):
            child = item.child(row)
            if child is not None:
                if child.rowCount() > 0:
                    if self.has_visible_items(child):
                        return True
                else:
                    return True
        return False
    
    @Slot(str, result=str)
    def get_color_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)

    @Slot(str, result=str)
    def get_image_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Image", key)

class SelectCamerasDialog(NewBaseDialog):
    def __init__(self, parent=None, schedule_controller:ScheduleController = None):
        self.schedule_controller = schedule_controller
        self.selectCamerasController = SelectCamerasController()
        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.quick_widget = QQuickWidget()

        self.quick_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)   
        self.quick_widget.engine().rootContext().setContextProperty("selectCamerasController", self.selectCamerasController)
        self.quick_widget.setSource(QUrl("qrc:src/common/qml/videoplayback/SelectCamerasDialog.qml"))
        self.quick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)

        layout.addWidget(self.quick_widget) # Cưỡng ép lấy focus khi khởi tạo
        widget_main = QWidget()
        widget_main.setLayout(layout)
        super().__init__(parent, title=self.tr("Select Cameras"), content_widget=widget_main,
                         width_dialog=600, max_height_dialog=600)
        self.setObjectName("SelectCamerasDialog")
        self.save_update_signal.connect(self.update_clicked)

    def update_clicked(self):
        treeModel = self.selectCamerasController.treeModel
        data = self.schedule_controller.get_data()
        root_tree_view = treeModel.invisibleRootItem()
        for i in range(root_tree_view.rowCount()):
            item_child = root_tree_view.child(i, 0)
            itemType = item_child.data(Qt.UserRole+5)
            if itemType == "Invalid":
                for i in range(item_child.rowCount()):
                    item_child1 = item_child.child(i, 0)
                    itemType1 = item_child1.data(Qt.UserRole+5)
                    if itemType1 == "Invalid":
                        for i in range(item_child1.rowCount()):
                            item_child2 = item_child1.child(i, 0)
                            itemType2 = item_child2.data(Qt.UserRole + 5)
                            if itemType2 == "Camera":
                                id = item_child2.data(Qt.UserRole + 1)
                                name = item_child2.data(Qt.DisplayRole)
                                checked = item_child2.data(Qt.UserRole + 4)
                                if checked:
                                    copy_dict = dict(data)
                                    self.update_recording_schedule(id = id,data = copy_dict)
                    elif itemType1 == "Camera":
                        id = item_child1.data(Qt.UserRole + 1)
                        name = item_child1.data(Qt.DisplayRole)
                        checked = item_child1.data(Qt.UserRole + 4)
                        if checked:
                            copy_dict = dict(data)
                            self.update_recording_schedule(id = id,data = copy_dict)
        self.close()    

    def update_recording_schedule(self,id = None, data = {}):
        # kiểm tra xem id camera này đã được khởi tạo cấu hình recording schedule chưa
        def callback(response):
            if response is not None:
                if len(response) > 0:
                    record_id = response[0].get("id",None)
                    data["id"] = record_id
                    data["cameraId"] = id
                    logger.debug(f'update_recording_schedule1 = {response}')
                    subThread = SubThread(parent=main_controller.list_parent["CameraScreen"],target=main_controller.current_controller.update_recording_schedule_by_patch,args=(data,))
                    subThread.start()
                else:
                    data["cameraId"] = id
                    subThread = SubThread(parent=main_controller.list_parent["CameraScreen"],target=main_controller.current_controller.create_recording_schedule,args=(data,))
                    subThread.start()

        subThread = SubThread(parent=main_controller.list_parent["CameraScreen"],target=main_controller.current_controller.get_recording_schedule,args=(None,id,),callback=callback)
        subThread.start()

    def closeEvent(self, event):
        self.selectCamerasController.closeChanged.emit()
        super().closeEvent(event)
