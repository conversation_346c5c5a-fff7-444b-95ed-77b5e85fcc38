import QtQuick
// import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Controls.Material 
import models 1.0
import '../map'
import QtQuick.Dialogs
Rectangle {
    id: root
    height: 200
    property color main_background: exportVideoData ? exportVideoData.get_color_theme_by_key("dialog_body_background") : "white"
    readonly property string search_not_found: exportVideoData ? exportVideoData.get_image_theme_by_key("search_not_found_qml") : ""
    property color normal_text_color: exportVideoData ? exportVideoData.get_color_theme_by_key("dialog_text") : "white"
    property color border_color: exportVideoData ? exportVideoData.get_color_theme_by_key("common_border") : "blue"
    property color treeview_on_hover: exportVideoData ? exportVideoData.get_color_theme_by_key("treeview_on_hover") : "blue"

    color: main_background

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 4
        // Video display area
        // Rectangle {
        //     id: topRect
        //     Layout.fillWidth: true
        //     Layout.fillHeight: true
        //     Layout.preferredHeight: 300
        //     color: "transparent"
        //     border.color: "#000000"
        //     border.width: 1
        //     radius: 4

        //     // Image {
        //     //     id: originImage
        //     //     anchors.centerIn: topRect
        //     //     width: topRect.width
        //     //     height: topRect.height
        //     //     source: root.itemData ? root.itemData.eventModel.imageFullUrl : ""
        //     //     fillMode: Image.PreserveAspectFit
        //     //     onStatusChanged: {
        //     //         if (status === Image.Ready) {
        //     //             isOriginImageReady = true
        //     //         }
        //     //     }
        //     // }

        //     // Text{
        //     //     anchors.centerIn: topRect
        //     //     text: qsTr("Loading Image...")
        //     //     font.pixelSize: Math.max(10, Math.min(14, root.width / 25))
        //     //     font.weight: Font.Medium
        //     //     color: "gray"
        //     //     visible: !isOriginImageReady
        //     // }


        // }
        Rectangle {
            id: bottomRect
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 200
            color: "transparent"
            ColumnLayout {
                id: colLayout
                anchors.fill: parent
                anchors.margins: 4
                RowLayout  {
                    id: topRow
                    Layout.fillWidth: true

                    TextField {
                        id: folderField
                        placeholderText: qsTr("Enter file path")
                        Layout.fillWidth: true
                        height: 30
                        verticalAlignment: Text.AlignVCenter
                        padding: 0
                        placeholderTextColor: normal_text_color
                        text: exportVideoData ? exportVideoData.filePath : ""
                        color: normal_text_color

                        background: Rectangle {
                            color: "transparent"
                            border.color: border_color
                            border.width: 1
                            radius: 4
                        }
                        onTextChanged: {
                            if (exportVideoData) {
                                exportVideoData.filePath = folderField.text
                            }
                        }
                    }
                    Button{
                        width: 50
                        height: 30
                        text: qsTr("Browse..")
                        font.pixelSize: 14
                        font.weight: Font.Bold
                        Material.foreground: normal_text_color
                        background: Rectangle{
                            radius: 6
                            color: "#ED4845"
                            MouseArea{
                                anchors.fill: parent
                                hoverEnabled: true
                                cursorShape: Qt.PointingHandCursor
                                onEntered: {
                                    parent.color = "#E03E3B"
                                }
                                onExited: {
                                    parent.color = "#ED4845"
                                }
                            }
                        }

                        onClicked: function(){
                            folderDialog.open()
                        }
                    }
                }
                Text {
                    anchors {
                        top: topRow.bottom
                    }
                    text: qsTr("Please enter the file path")
                    color: "red"
                    font.pixelSize: 10
                    visible: exportVideoData ? exportVideoData.filePathWarn : false
                }
                RowLayout  {
                    id: bottomRow
                    Layout.fillWidth: true

                    TextField {
                        id: fileNameField
                        placeholderText: qsTr("Enter file name")
                        Layout.fillWidth: true
                        height: 30
                        verticalAlignment: Text.AlignVCenter
                        padding: 0
                        text: exportVideoData ? exportVideoData.fileName : ""
                        placeholderTextColor: normal_text_color
                        color: normal_text_color

                        background: Rectangle {
                            color: "transparent"
                            border.color: border_color
                            border.width: 1
                            radius: 4
                        }
                        onTextChanged: {
                            if (exportVideoData) {
                                exportVideoData.fileName = fileNameField.text
                            }
                        }
                    }
                    // Button{
                    //     width: 50
                    //     height: 30
                    //     text: qsTr(".mp4")
                    //     font.pixelSize: 14
                    //     font.weight: Font.Bold
                    //     Material.foreground: normal_text_color
                    //     background: Rectangle{
                    //         radius: 6
                    //         color: "#ED4845"
                    //         MouseArea{
                    //             anchors.fill: parent
                    //             hoverEnabled: true
                    //             cursorShape: Qt.PointingHandCursor
                    //             onEntered: {
                    //                 parent.color = "#E03E3B"
                    //             }
                    //             onExited: {
                    //                 parent.color = "#ED4845"
                    //             }
                    //         }
                    //     }

                    //     onClicked: function(){

                    //     }
                    // }
                }
                Text {
                    anchors {
                        top: bottomRow.bottom
                    }
                    text: qsTr("Please enter the file name")
                    color: "red"
                    font.pixelSize: 10
                    visible: exportVideoData ? exportVideoData.fileNameWarn : false
                }
            }
        }
    }

    FolderDialog {
        id: folderDialog
        title: qsTr("Chọn thư mục lưu file")
        onAccepted: {
            var folder = folderDialog.selectedFolder
            if (folder && folder.toString) {
                folder = folder.toString()
                if (folder.indexOf("file:///") === 0) {
                    folder = folder.replace("file:///", "")
                }
            }
            folderField.text = folder ? folder : ""
        }
    }
}
