from PySide6.QtCore import QUrl,Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout
from PySide6.QtQuickWidgets import QQuickWidget
import os
from pathlib import Path
from src.styles.style import Style
from src.common.model.tab_model import TabModel
from PySide6.QtGui import QShortcut,QKeySequence
from src.common.qml.models.map_controller import MapState
from src.common.qml.models.common_enum import CommonEnum
from src.common.qml.models.map_controller import FloorModel,floor_manager,BuildingModel,building_manager,MapModel,map_manager,Map2DController
from src.common.controller.main_controller import main_controller
from src.common.widget.notifications.notify import Notifications
from src.common.qml.models.grid_model import GridModel
from src.presentation.camera_screen.managers.grid_manager import gridManager
import logging
logger = logging.getLogger(__name__)
class CameraGridWidget(QWidget):
    def __init__(self, parent=None, gridModel: GridModel = None):
        super().__init__(parent)
        
        # Store tab model
        self.gridModel: GridModel = gridModel
        logger.debug(f"[DEBUG_GRID] CAMERA_GRID: gridModel = {self.gridModel}")
        logger.debug(f"[DEBUG_GRID] CAMERA_GRID: gridModel.get_property('id') = {self.gridModel.get_property('id')}")
        # Create layout
        self._layout = QVBoxLayout(self)
        self._layout.setContentsMargins(0, 0, 0, 0)
        self._quick_widget = self.loadQML()
        # Add widget to layout
        self._layout.addWidget(self._quick_widget)
        self.shortcut = QShortcut(QKeySequence(Qt.CTRL | Qt.Key_S), self)
        self.shortcut.activated.connect(self.shortcut_activated)

    def loadQML(self):
        if self.gridModel.get_property("type") == CommonEnum.TabType.MAPVIEW:
            self.map_model = map_manager.get_map_model(id=self.gridModel.get_property("id"))
            self.mapState = MapState()
            self.mapState.editMode = True
            self.mapState.viewModeChanged.connect(self.viewModeChanged)
            self.mapState.editModeChanged.connect(self.editModeChanged)
            self.mapState.notifyChanged.connect(self.notifyChanged)
            quick_widget = QQuickWidget(self)
            if self.mapState.editMode:
                self.clone_map_model = self.map_model.clone()
                self.clone_map_model.notifyChanged.connect(self.notifyChanged)
            # logger.debug(f'MapGridWidgetItem1 = {self.clone_map_model}')
            quick_widget.rootContext().setContextProperty("mapModel", self.clone_map_model if self.mapState.editMode else self.map_model)
            quick_widget.rootContext().setContextProperty("mapState", self.mapState)
            quick_widget.rootContext().setContextProperty("gridModel", self.gridModel)
            quick_widget.setSource(QUrl("qrc:src/common/qml/map/MapOnGrid.qml"))
            quick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)
            # quick_widget.setGeometry(0, 0, self.width, self.height)
            quick_widget.setAcceptDrops(True)
            return quick_widget
        elif self.gridModel.get_property("type") == CommonEnum.TabType.FLOORVIEW:
            self.map_state = MapState()
            self.map_state.editMode = True
            self.floor_model = floor_manager.getFloor(id=self.gridModel.get_property("id"))
            self.map_controller = Map2DController(self.floor_model, self.map_state)
            quick_widget = QQuickWidget(self)
            self.map_controller.saveCameraStatusSignal.connect(self.saveCameraStatusSignal)
            self.map_controller.listCameraChanged.connect(self.changeSaveStateGridModel)
            quick_widget.rootContext().setContextProperty("map2dController", self.map_controller)
            # quick_widget.rootContext().setContextProperty("gridModel", self.gridModel)
            quick_widget.setSource(QUrl("qrc:/src/common/qml/map/Map2DonGrid.qml"))
            quick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)
            quick_widget.setAcceptDrops(True)
            return quick_widget
        else:
            self.gridModel.set_property("isShow", True)
            self.gridModel.loadData()
            quick_widget = QQuickWidget(self)
            quick_widget.setResizeMode(QQuickWidget.ResizeMode.SizeRootObjectToView)

            engine = quick_widget.engine()
            root_context = engine.rootContext()
            root_context.setContextProperty("gridModel", self.gridModel)
            # Load QML source AFTER setting context
            quick_widget.setSource(QUrl("qrc:/src/presentation/camera_screen/components/MainGrid.qml"))

            # Check for QML loading errors
            if quick_widget.status() == QQuickWidget.Error:
                errors = quick_widget.errors()
                logger.debug("QML Loading Errors:")
                for error in errors:
                    logger.debug(f"  - {error}")
            else:
                # Try to call setGridModel function on QML root object
                root_object = quick_widget.rootObject()
                if root_object:
                    try:
                        # Call QML function to set gridModel
                        root_object.setGridModel(self.gridModel)
                    except Exception as e:
                        logger.error(f"❌ Error calling setGridModel: {e}")
                else:
                    logger.error(f"❌ No root object found")

            return quick_widget
        
    def changeSaveStateGridModel(self):
        self.gridModel.isSave = False

    def viewModeChanged(self):
        if not self.mapState.editMode:
            if self.mapState.viewMode == self.mapState.ViewMode.FULLSCREEN:
                self.header_top_widget.setVisible(False)
                self.header_top_widget.setGeometry(0, 0, 0, 0)
            else:
                self.header_top_widget.setVisible(True)
                self.header_top_widget.setGeometry(0, 0, self.root_width, self.HEIGHT_HEADER)
            
    def editModeChanged(self):
        logger.debug(f'editModeChanged = {self.mapState.viewMode,self.mapState.editMode,self.mapState.EditMode.EDITABLE}')

    def notifyChanged(self,notifyKey):
        if notifyKey == MapState.notifyKey.SelectGridType:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('Please enlarge the grid cell or switch to a grid size smaller than 3x3.'), icon=Style.PrimaryImage.info_result)
        elif notifyKey == MapState.notifyKey.SelectEditMode:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('To drag an item onto the Map, you need to enter Map edit mode.'), icon=Style.PrimaryImage.info_result)
        elif notifyKey == MapState.notifyKey.MapSavedSuccessfully:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('Map saved successfully.'), icon=Style.PrimaryImage.sucess_result)
        elif notifyKey == MapState.notifyKey.MapSaveFailed:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('Failed to save map.'), icon=Style.PrimaryImage.info_result)
        elif notifyKey == MapState.notifyKey.LocationAlert:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('This camera is already assigned to another position on the map.'), icon=Style.PrimaryImage.info_result)
            
    def saveCameraStatusSignal(self, status):
        if status:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                            title=self.tr('Floor saved successfully.'), icon=Style.PrimaryImage.sucess_result)
        else:
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                            title=self.tr('Failed to save floor.'), icon=Style.PrimaryImage.fail_result)
            
    def update_map_model(self):
        self.map_model.merge(self.clone_map_model)
        # self.mapState.notifyChanged.emit(MapState.notifyKey.MapSavedSuccessfully)

    def shortcut_activated(self):   
        self.gridModel.shortcut_activated()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self._quick_widget.setGeometry(0, 0, self.width(), self.height())

    def mousePressEvent(self, event):
        """✅ FIX: Handle mouse press events to clear camera selection when clicking outside QML area"""
        super().mousePressEvent(event)

        # Clear camera selection when clicking outside QML area
        if hasattr(self, 'gridModel') and self.gridModel:
            # Call GridModel method to emit signal
            self.gridModel.clearAllSelection()
        
    def switch_tab_model(self, tab_model: TabModel):
        if self.tab_model is not None:
            self.tab_model.unregister_signal(self)
        self.tab_model = tab_model
        if self.tab_model is not None:
            self.tab_model.data.isShow = True
            self.tab_model.register_signal(self)
            # Update QML context with new tab model
            context = self._quick_widget.engine().rootContext()
            context.setContextProperty("tabModel", self.tab_model) 
        # Đảm bảo cập nhật trạng thái isSingleGridItem trước khi truyền vào QML
        if hasattr(self.gridModel, '_updateIsSingleGridItem'):
            self.gridModel._updateIsSingleGridItem() 
