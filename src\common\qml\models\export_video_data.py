from PySide6.QtCore import QObject, Property, Signal,Slot
from datetime import datetime, timedelta
from src.common.qml.models.ruler_context import RulerContext
from src.common.controller.main_controller import main_controller
import logging
logger = logging.getLogger(__name__)

class ExportVideoData(QObject):
    filePathChanged = Signal()
    fileNameChanged = Signal()
    filePathWarnChanged = Signal()
    fileNameWarnChanged = Signal()
    def __init__(self, data = None):
        super().__init__()
        self.data = data
        self._filePath = ""
        self._fileName = ""
        self._filePathWarn = False
        self._fileNameWarn = False


    @Property(str,notify=filePathChanged)
    def filePath(self):
        return self._filePath
    
    @filePath.setter
    def filePath(self, value: str):
        if self._filePath != value:
            self._filePath = value
            self.filePathChanged.emit() 

    @Property(str,notify=fileNameChanged)
    def fileName(self):
        return self._fileName
    
    @fileName.setter
    def fileName(self, value: str):
        if self._fileName != value:
            self._fileName = value
            self.fileNameChanged.emit() 

    @Property(bool,notify=filePathWarnChanged)
    def filePathWarn(self):
        return self._filePathWarn
    
    @filePathWarn.setter
    def filePathWarn(self, value: bool):
        if self._filePathWarn != value:
            self._filePathWarn = value
            self.filePathWarnChanged.emit() 

    @Property(bool,notify=fileNameWarnChanged)
    def fileNameWarn(self):
        return self._fileNameWarn
    
    @fileNameWarn.setter
    def fileNameWarn(self, value: bool):
        if self._fileNameWarn != value:
            self._fileNameWarn = value
            self.fileNameWarnChanged.emit() 

    @Slot(str, result=str)
    def get_color_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)

    @Slot(str, result=str)
    def get_image_theme_by_key(self, key=None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Image", key)