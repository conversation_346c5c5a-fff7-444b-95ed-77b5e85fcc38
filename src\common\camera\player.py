import av
import ctypes
import threading
from abc import abstractmethod
from PySide6.QtGui import QImage, QPixmap
from PySide6.QtCore import Signal, QThread
from src.presentation.device_management_screen.widget.ai_state import AIFlowType
from src.common.model.camera_model import CameraModel
from src.common.qml.models.common_enum import CommonEnum
import logging
logger = logging.getLogger(__name__)
logging.basicConfig()
logging.getLogger('libav').setLevel(level=logging.CRITICAL)
av.logging.set_level(av.logging.PANIC)

class StreamCameraType:
    main_stream = "Main Stream"
    sub_stream = "Sub Stream"
    recognition_stream = "Recognition Stream"
    protection_stream = "Protection Stream"
    frequency_stream = "Frequency Stream"
    access_stream = "Access Stream"
    motion_stream = "Motion Stream"
    traffic_stream = "Traffic Stream"
    weapon_stream = "Weapon Stream"
    ufo_stream = "UFO Stream"
    video_stream = "video_stream"

    @staticmethod
    def get_stream_type(ai_type):
        if ai_type == AIFlowType.RECOGNITION:
            return StreamCameraType.recognition_stream
        elif ai_type == AIFlowType.PROTECTION:
            return StreamCameraType.protection_stream
        elif ai_type == AIFlowType.FREQUENCY:
            return StreamCameraType.frequency_stream
        elif ai_type == AIFlowType.ACCESS:
            return StreamCameraType.access_stream
        elif ai_type == AIFlowType.MOTION:
            return StreamCameraType.motion_stream
        elif ai_type == AIFlowType.TRAFFIC:
            return StreamCameraType.traffic_stream
        elif ai_type == AIFlowType.UFO:
            return StreamCameraType.ufo_stream
        return StreamCameraType.recognition_stream

    @staticmethod
    def get_ai_type(ai_stream):
        if ai_stream == StreamCameraType.recognition_stream:
            return AIFlowType.RECOGNITION
        elif ai_stream == StreamCameraType.protection_stream:
            return AIFlowType.PROTECTION
        elif ai_stream == StreamCameraType.frequency_stream:
            return AIFlowType.FREQUENCY
        elif ai_stream == StreamCameraType.access_stream:
            return AIFlowType.ACCESS
        elif ai_stream == StreamCameraType.motion_stream:
            return AIFlowType.MOTION
        elif ai_stream == StreamCameraType.traffic_stream:
            return AIFlowType.TRAFFIC
        elif ai_stream == StreamCameraType.weapon_stream:
            return AIFlowType.WEAPON
        elif ai_stream == StreamCameraType.ufo_stream:
            return AIFlowType.UFO
        return None

class CameraState:
    paused = 'paused'
    started = 'started'
    stopped = 'stopped'
    connecting = 'connecting'
    unauthorized = 'unauthorized'

# Định nghĩa kiểu callback
VideoLockCB = ctypes.CFUNCTYPE(ctypes.c_void_p, ctypes.c_void_p, ctypes.POINTER(ctypes.POINTER(ctypes.c_uint8)))
VideoUnlockCB = ctypes.CFUNCTYPE(None, ctypes.c_void_p, ctypes.c_void_p, ctypes.POINTER(ctypes.POINTER(ctypes.c_uint8)))
VideoDisplayCB = ctypes.CFUNCTYPE(None, ctypes.c_void_p, ctypes.c_void_p)

class Player(QThread):
    share_frame_signal = Signal(tuple)
    camera_state_signal = Signal(str)
    buffering_signal = Signal(float)
    media_loaded_signal = Signal()
    next_chunk_signal = Signal(str)
    
    MAX_WIDTH_VIDEO_DECODE = 1280
    MAX_HEIGHT_VIDEO_DECODE = 720
    ENABLE_DEBUG = False

    def __init__(self,parent = None, camera_id=None, camera_model: CameraModel = None, stream_type=CommonEnum.StreamType.MAIN_STREAM, height=0, width=0, uuid = ''):
        super().__init__(parent)
        logger.debug(f'Khởi tạo Player: Camera ID={camera_id}, Stream={stream_type}')
        
        # Các thuộc tính cơ bản
        self.list_resize_frame = {}
        self.camera_id = camera_id
        self.camera_model = camera_model
        logger.debug(f'Player: Camera Model={camera_model}')
        self.connect_status = True
        self.camera_state = CameraState.connecting
        self.stream_type = None
        self.start_time = None
        self.is_send_mat_frame = False
        self.max_width_preview_available = 0
        self.max_height_preview_available = 0
        
        # Các thuộc tính khác
        if width is not None and height is not None and width > 0 and height > 0:
            self.update_resize(width, height, uuid)
        self.registered_widgets = []
        self.is_pause_live = False
        self.is_pause_video = False

        self.stream_link_event = threading.Event()  # Event to wait for stream link
        self._stop_thread = False  # To allow clean exit if needed

    def register_signal(self,widget = None):
        logger.debug(f'register_signal')
        if widget is not None and widget not in self.registered_widgets:
            logger.debug(f'Registering signals for widget {widget.uuid} on camera {self.camera_id}')
            if hasattr(widget, 'share_frame_signal'):
                self.share_frame_signal.connect(widget.share_frame_signal)
            if hasattr(widget, 'camera_state_signal'):
                self.camera_state_signal.connect(widget.camera_state_signal) 
            if hasattr(widget, 'next_chunk_signal'):
                self.next_chunk_signal.connect(widget.next_chunk_signal) 
            if hasattr(widget, 'buffering_signal'):
                self.buffering_signal.connect(widget.buffering_signal)
            self.registered_widgets.append(widget)

    @abstractmethod
    def unregister_signal(self,widget = None):
        """Safely unregister widget signals and cleanup if needed"""
        if widget is not None:
            try:
                # Disconnect signals
                if hasattr(widget, 'share_frame_signal'):
                    try:
                        self.share_frame_signal.disconnect(widget.share_frame_signal)
                    except Exception as e:
                        logger.warning(f'Error disconnecting share_frame_signal: {e}')
                        
                if hasattr(widget, 'camera_state_signal'):
                    try:
                        self.camera_state_signal.disconnect(widget.camera_state_signal)
                    except Exception as e:
                        logger.warning(f'Error disconnecting camera_state_signal: {e}')
                        
                if hasattr(widget, 'buffering_signal'):
                    try:
                        self.buffering_signal.disconnect(widget.buffering_signal)
                    except Exception as e:
                        logger.warning(f'Error disconnecting buffering_signal: {e}')
            except Exception as e:
                logger.error(f'Error during widget unregistration: {e}')

    @abstractmethod
    def play_live(self):
        self.is_pause_live = False
        logger.debug(f'Camera {self.camera_id}: Resuming live stream')
        pass
    @abstractmethod
    def pause_live(self):
        logger.debug(f'Camera {self.camera_id}: Pausing live stream')
        self.is_pause_live = True

    @abstractmethod
    def stop_live(self):
        pass

    @abstractmethod
    def stop_capture(self):
        pass

    @abstractmethod
    def stop_video(self):
        pass

    @abstractmethod
    def reset_thread(self):
        pass

    @abstractmethod
    def load_media(self, media_path, vlc_options=None, seek_time=None,start_duration = None,end_duration = None):
        pass

    def update_resize(self, width=None, height=None, uuid = '', remove=False, is_fullscreen = False):
        logger.debug(f'Camera {self.camera_id}: Updating resize - Width={width}, Height={height}, Widget={uuid}, Remove={remove} - is_fullscreen: {is_fullscreen}')
        if remove and uuid in self.list_resize_frame:
            self.list_resize_frame.pop(uuid)
        else:
            self.list_resize_frame[uuid] = (width, height)
        max_width = 0
        max_height = 0
        # find biggest width and height in list_resize_frame
        logger.debug(f'update_resize: list_resize_frame: {self.list_resize_frame}')
        for key, value in self.list_resize_frame.items():
            w, h = value
            if w > max_width:
                max_width = w
            if h > max_height:
                max_height = h
        # if max_width > self.MAX_WIDTH_VIDEO_DECODE: and max_height > self.MAX_HEIGHT_VIDEO_DECODE: 
        # -> resize to MAX_WIDTH_VIDEO_DECODE and MAX_HEIGHT_VIDEO_DECODE
        if max_width > self.MAX_WIDTH_VIDEO_DECODE:
            max_width = self.MAX_WIDTH_VIDEO_DECODE
        if max_height > self.MAX_HEIGHT_VIDEO_DECODE:
            max_height = self.MAX_HEIGHT_VIDEO_DECODE
        
        logger.debug(f'update_resize: max_width: {max_width} - max_height: {max_height}')
        self.max_width_preview_available = max_width
        self.max_height_preview_available = max_height
        
        # Cập nhật độ phân giải Media Player - Tạm thời chưa xử lý tối ưu sau
        # self.update_media_player_resolution(is_fullscreen)

        logger.debug(f'Camera {self.camera_id}: New dimensions - Max Width={self.max_width_preview_available}, Max Height={self.max_height_preview_available}')

    @staticmethod
    def mat_to_q_pixmap(mat):
        h, w, c = mat.shape
        d = mat.dtype.itemsize
        s = c * w * d
        img = QImage(
            mat, w, h, s, QImage.Format_RGB888).rgbSwapped()
        return QPixmap.fromImage(img)

    def play_video(self):
        pass

    def start_video(self):
        pass

    def set_send_mat_frame(self, allow):
        self.is_send_mat_frame = allow

    def __del__(self):
        """Destructor to ensure cleanup when object is destroyed"""
        try:
            logger.debug(f'Camera {self.camera_id}: Destructor called')
            self.stop_capture()
        except Exception as e:
            logger.error(f'Error during Player destruction: {e}')

    def calculate_target_dimensions(self, video_width, video_height, max_width_preview_available, max_height_preview_available):
        """Tính toán kích thước mục tiêu dựa trên aspect ratio của video"""
        if video_width <= 0 or video_height <= 0:
            # Sử dụng aspect ratio mặc định 16:9 nếu không lấy được kích thước video
            aspect_ratio = 16/9
            target_width = max_width_preview_available
            target_height = int(self.target_width / aspect_ratio)
            # Đảm bảo kích thước chia hết cho 4 để tránh lỗi padding
            target_width = self.make_divisible_by_four(target_width)
            target_height = self.make_divisible_by_four(target_height)
            logger.debug(f'Camera {self.camera_id}: Sử dụng aspect ratio mặc định - {target_width}x{target_height}')
            return target_width, target_height

        # Tính toán aspect ratio gốc
        original_aspect_ratio = float(video_width) / float(video_height)
        # logger.debug(f'calculate_target_dimensions: original_aspect_ratio: {original_aspect_ratio}')
        
        # Tính toán kích thước mới dựa trên aspect ratio
        if original_aspect_ratio > 1:  # Landscape
            target_width = max_width_preview_available
            target_height = int(max_width_preview_available / original_aspect_ratio)
        else:  # Portrait
            target_height = max_height_preview_available
            target_width = int(target_height * original_aspect_ratio)
        
        # Đảm bảo kích thước chia hết cho 4 để tránh lỗi padding
        target_width = self.make_divisible_by_four(target_width)
        target_height = self.make_divisible_by_four(target_height)
            
        # logger.debug(f'Camera {self.camera_id}: Aspect ratio gốc: {original_aspect_ratio:.2f} - Kích thước mới: {target_width}x{target_height}')
        return target_width, target_height

    def make_divisible_by_four(self, number):
        """Đảm bảo số chia hết cho 4"""
        return number + (4 - (number % 4)) % 4
