from typing import List
from src.common.model.zone_model import ZoneModel
#from src.common.model.camera_model import Camera
# object Camera Model dùng để lưu trữ thông tin của Camera đã khớp với dữ liệu trả về từ API
# và dùng để chuyển đổi thành dữ liệu để hiển thị trên UI
# Không được thay đổi các thuộc tính của object này trừ khi Backend API thay đổi
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Signal
from src.presentation.device_management_screen.widget.ai_state import AIFlowType

@dataclass
class AiFlow:
    id: str = None
    name: str = None
    apply: bool = None
    alert: bool = None
    type: str = None
    state: str = None
    cameraGroupId: str = None
    cameraId: str = None
    cameraDTO: dict = None
    polygonIds: List[str] = None
    polygonDTOs: List[dict] = None
    threshold: float = None # Ngưỡng tin cậy
    interval: float = None # Tần suất cập nhật
    stopDuration: float = None # Thời gian dừng (Giây) (MOTION)
    disappearDuration: float = None # Thời gian biến mất (Giây) (ACCESS)
    appearanceCount: int = None # Số lần xuất hiện (Lần) (FREQUENCY)
    trackingDuration: int = None # Thời gian theo dõi (Phút) (FREQUENCY)
    objects: List[str] = None
    
    # Internal field for routing
    server_ip: str = None

    def __post_init__(self):
        # Fix the issue of mutable default arguments
        if self.objects is None:
            self.objects = []
        if self.polygonDTOs is None:
            self.polygonDTOs = []
        if self.polygonIds is None:
            self.polygonIds = []

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None and k != 'server_ip'}
    
    def is_apply(self):
        return self.apply

    def is_recognition_and_protection(self):
        return self.is_recognition() or self.is_protection() or self.is_frequency() or self.is_access() or self.is_motion() or self.is_traffic()
    
    def is_risk_identification(self):
        return self.is_weapon() or self.is_ufo() or self.is_fire()

    def is_recognition(self):
        return self.type == AIFlowType.RECOGNITION.__str__()
    
    def is_protection(self):
        return self.type == AIFlowType.PROTECTION.__str__()
    
    def is_frequency(self):
        return self.type == AIFlowType.FREQUENCY.__str__()
    
    def is_access(self):
        return self.type == AIFlowType.ACCESS.__str__()
    
    def is_motion(self):
        return self.type == AIFlowType.MOTION.__str__()
    
    def is_traffic(self):
        return self.type == AIFlowType.TRAFFIC.__str__()
    
    def is_weapon(self):
        return self.type == AIFlowType.WEAPON.__str__()
    
    def is_ufo(self):
        return self.type == AIFlowType.UFO.__str__()

    def is_fire(self):
        return self.type == AIFlowType.FIRE.__str__()

    def is_human_recognition(self):
        """Check if HUMAN object type is enabled in this AI flow"""
        if self.objects is None:
            return False
        return "HUMAN" in self.objects

    def is_vehicle_recognition(self):
        """Check if VEHICLE object type is enabled in this AI flow"""
        if self.objects is None:
            return False
        return "VEHICLE" in self.objects

    def clear(self):
        self.objects = []

class AiFlowModel(QObject):
    change_aiflow_model = Signal(tuple)
    def __init__(self,aiflow: AiFlow = None):
        super().__init__()
        self.data = aiflow

    def set_name(self, name):
        self.data.name = name
        self.change_aiflow_model.emit(('name', name, self))

    def set_apply(self, value):
        self.data.apply = value
        self.change_aiflow_model.emit(('apply', value, self))

    def set_alert(self, value):
        self.data.alert = value
        self.change_aiflow_model.emit(('alert', value, self))

    def set_type(self, value):
        self.data.type = value
        self.change_aiflow_model.emit(('type', value, self))

    def set_state(self, value):
        self.data.state = value
        self.change_aiflow_model.emit(('state', value, self))

    def set_camera_group_id(self, value):
        self.data.cameraGroupId = value
        self.change_aiflow_model.emit(('cameraGroupId', value, self))

    def set_camera_id(self, value):
        self.data.cameraId = value
        self.change_aiflow_model.emit(('cameraId', value, self))

    def set_camera_dto(self, value):
        self.data.cameraDTO = value
        self.change_aiflow_model.emit(('cameraDTO', value, self))

    def set_polygon_ids(self, value):
        self.data.polygonIds = value
        self.change_aiflow_model.emit(('polygonIds', value, self))

    def set_polygon_dtos(self, value):
        self.data.polygonDTOs = value
        self.change_aiflow_model.emit(('polygonDTOs', value, self))

    def set_threshold(self, value):
        self.data.threshold = value
        self.change_aiflow_model.emit(('threshold', value, self))

    def set_interval(self, value):
        self.data.interval = value
        self.change_aiflow_model.emit(('interval', value, self))

    def set_objects(self, value):
        self.data.objects = value
        self.change_aiflow_model.emit(('objects', value, self))

    def diff_aiflow_model(self, aiflow:AiFlow = None):
        dict1 = asdict(self.data)
        dict2 = asdict(aiflow)
        diff = []
        for field, value in dict2.items():
            if dict1[field] != value:
                if field == 'name':
                    diff.append(field)
                    self.set_name(value)
                elif field == 'apply':
                    diff.append(field)
                    self.set_apply(value)
                elif field == 'alert':
                    diff.append(field)
                    self.set_alert(value)
                elif field == 'type':
                    diff.append(field)
                    self.set_type(value)
                elif field == 'state':
                    diff.append(field)
                    self.set_state(value)
                elif field == 'cameraGroupId':
                    diff.append(field)
                    self.set_camera_group_id(value)
                elif field == 'cameraId':
                    diff.append(field)
                    self.set_camera_id(value)
                elif field == 'cameraDTO':
                    diff.append(field)
                    self.set_camera_dto(value)
                elif field == 'polygonIds':
                    diff.append(field)
                    self.set_polygon_ids(value)
                elif field == 'polygonDTOs':
                    diff.append(field)
                    self.set_polygon_dtos(value)
                elif field == 'threshold':
                    diff.append(field)
                    self.set_threshold(value)
                elif field == 'interval':
                    diff.append(field)
                    self.set_interval(value)
                elif field == 'objects':
                    diff.append(field)
                    self.set_objects(value)
                else:
                    diff.append(field)
                    if field != 'server_ip':  # Skip internal routing field
                        setattr(self.data, field, value)
        return diff

class AiFlowModelManager(QObject):
    add_aiflows_signal = Signal(tuple)
    delete_camera_model_signal = Signal(list)
    add_aiflow_signal = Signal(tuple)
    add_cameras_signal = Signal(list)
    __instance = None
    def __init__(self):
        super().__init__()
        self.aiflows = {}

    @staticmethod
    def get_instance():
        if AiFlowModelManager.__instance is None:
            AiFlowModelManager.__instance = AiFlowModelManager()
        return AiFlowModelManager.__instance

    def add_aiflows(self,aiflows:List[AiFlowModel] = [],controller = None):
        output = {}
        for aiflow_model in aiflows:
            aiflow_model.data.server_ip = controller.server.data.server_ip
            output[aiflow_model.data.id] = aiflow_model
        # print(f'add_camera_list = {camera_list}')
        self.aiflows[controller.server.data.server_ip] = output
        self.add_aiflows_signal.emit((controller,aiflows))

    def add_aiflow(self, controller=None, aiflow:AiFlowModel=None):
        # Initialize dictionary for new server_ip if it doesn't exist
        if aiflow.data.server_ip not in self.aiflows:
            self.aiflows[aiflow.data.server_ip] = {}

        aiflow_result = self.get_aiflow_model(id=aiflow.data.id)
        if aiflow_result is None:
            self.aiflows[aiflow.data.server_ip][aiflow.data.id] = aiflow
        self.add_aiflow_signal.emit((aiflow))

    def get_aiflow_model(self,id = None):
        if id is not None:
            for idx,aiflows in self.aiflows.items():
                result = aiflows.get(id,None)
                if result is not None:
                    return result
        return None

    def get_aiflows(self,server_ip = None,cameraIds = None,cameraGroupIds = None):
        output = {}

        if server_ip in self.aiflows:
            if cameraIds is not None:
                for id, aiflow_model in self.aiflows[server_ip].items():
                    if aiflow_model.data.cameraId is not None and cameraIds in aiflow_model.data.cameraId:
                        output[id] = aiflow_model
            elif cameraGroupIds is not None:
                for id, aiflow_model in self.aiflows[server_ip].items():
                    if aiflow_model.data.cameraGroupIds is not None and cameraGroupIds in aiflow_model.data.cameraGroupIds:
                        output[id] = aiflow_model
            else:
                output = self.aiflows[server_ip]
            return output
        else:
            return {}

    def delete_server(self,server_ip = None):
        if server_ip in self.aiflows:
            del self.aiflows[server_ip]

    def update_aiflows(self, aiflows:List[AiFlowModel] = []):
        print(f'update_aiflows')
        for aiflow in aiflows:
            for aiflow_model in self.aiflows[aiflow.data.server_ip].values():
                if aiflow.data.id == aiflow_model.data.id:
                    # print(f'update_camera_list = {camera.data.name} -- {camera.data.aiFlowIds} -- {camera_model.data.aiFlowIds}')
                    aiflow_model.diff_aiflow_model(aiflow=aiflow.data)
                    break
aiflow_model_manager = AiFlowModelManager.get_instance()