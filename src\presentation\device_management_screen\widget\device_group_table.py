import logging
logger = logging.getLogger(__name__)
from PySide6.QtWidgets import <PERSON>Widget, QCheckBox, QVBoxLayout, QHBoxLayout
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtCore import Qt, QUrl
from src.common.model.group_model import group_model_manager,GroupModel
from src.common.model.camera_model import camera_model_manager,CameraModel
from src.styles.style import Style
from src.common.model.device_models import  FilterType
from queue import Queue
from src.common.onvif_api.worker_thread import WorkerThread
from typing import List
from src.presentation.device_management_screen.widget.ai_state import AIType,AIFlowType
from src.common.controller.controller_manager import Controller
from src.common.widget.pagination.page_indicator.page_indicator import Pagination
from src.common.qml.models.device_controller import DeviceController,ListModel,ModelType,AIState,ItemRoles
from src.common.model.aiflows_model import <PERSON><PERSON><PERSON>,aiflow_model_manager
from src.common.controller.main_controller import main_controller,connect_slot

class DeviceGroupTable(QWidget):
    def __init__(self, parent=None,controller:Controller = None, items_per_page=10, data=None, row=10, column=7, header_data=[],width = None, height = None):
        super().__init__(parent)
        self.count = 0
        self.controller = controller
        self.page_indicator = None
        self.tab_divice_widget = parent
        # group_model_manager.delete_group_model_signal.connect(self.delete_group_model)
        # group_model_manager.add_group_signal.connect(self.add_group_signal)
        self.widget_width = width
        self.widget_height = height
        self.controller.update_state_delegate_group = self.update_state_delegate
        self.header_data = header_data
        self.items_per_page = items_per_page
        self.data = data if data is not None else []
        ##############################
        self.filter_ai = False
        self.filter_device_group_name = False
        self.is_loading = False
        # self.fitler_camera_thread = None
        self.filter_queue = Queue()
        self.fitler_camera_group_thread = WorkerThread(parent=self, target= self.update_data_to_table,args=(True,), callback=self.callback_filter_device_group)
        
        ###############################
        self.setup_ui()
        self.set_dynamic_stylesheet()

    def setup_ui(self):
        layout = QVBoxLayout()

        # add QML
        self.device_controller = DeviceController(parent=self)
        self.quick_widget = QQuickWidget()
        self.quick_widget.engine().rootContext().setContextProperty('device_controller', self.device_controller)
        self.quick_widget.setSource(QUrl("qrc:src/common/qml/device_table/DeviceGroupTable.qml"))

        self.quick_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)

        self.current_page = 1
        # Page Indicator:
        self.page_indicator = self.create_pagination()
        self.widget_indicator = QWidget()
        self.widget_indicator.setObjectName('widget_indicator')
        # self.widget_indicator.setStyleSheet(
        #     f"""
        #                         QWidget {{
        #                         background-color: {Style.PrimaryColor.background};
        #                         color: {Style.PrimaryColor.text_unselected};
        #                     }}
        #                 """)
        layout_indicator = QHBoxLayout()
        layout_indicator.setContentsMargins(0, 0, 0, 0)
        layout_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_indicator.addWidget(self.page_indicator)
        self.widget_indicator.setLayout(layout_indicator)

        # add to layout
        # layout.addWidget(self.header_widget)
        layout.addWidget(self.quick_widget)
        layout.addWidget(self.widget_indicator)
        layout.setStretch(0, 94)
        layout.setStretch(1, 6)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        self.setLayout(layout)
        self.is_group_updating = False

    def create_pagination(self):
        page_indicator = Pagination(rows_per_page=15)
        page_indicator.signal_update_table.connect(self.signal_update_table)
        if page_indicator is not None:
            page_indicator.set_total_rows_and_total_pages(len(self.controller.device_group_data_filtered))
        return page_indicator
    
    def signal_update_table(self, data):
        self.update_table()

    def callback_filter_device_group(self, data = None):
        self.update_table()

    def put_filter_queue(self, msg):
        self.filter_queue.put(msg)
        self.update_data_to_table()

    def update_data_to_table(self,is_thread = True):
        data = group_model_manager.get_group_list(server_ip=self.controller.server.data.server_ip)
        group_list = []
        # device group Table chỉ show data của group thôi, còn AIBox để show bên device table
        for group_model in data.values():
            if group_model.get_property("type") != "AI_BOX":
                group_list.append(group_model)
        if is_thread:
            while not self.filter_queue.empty():
                msg = self.filter_queue.get()
                if not self.is_loading:
                    self.is_loading = True
                    self.controller.device_group_data_filtered = []
                    if not self.filter_device_group_name and not self.filter_ai:
                        self.controller.device_group_data_filtered = group_list[::-1]
                        self.is_loading = False
                        self.callback_filter_device_group()
                        return
                    # device_screen = main_controller.list_parent['DeviceScreen']
                    # filter AI ###########################################
                    ai_dist = []
                    if self.filter_ai:
                        ai_type = self.tab_divice_widget.search_ai_group.combobox.currentIndex()
                        ai_type = self.get_ai_type(ai_type)
                        for item in group_list:
                            is_human,is_vehicle = item.data.aiApply()
                            if ai_type == is_human or ai_type == is_vehicle:
                                ai_dist.append(item)
                    else:
                        ai_dist = group_list

                    # filter camera name ###################################
                    camera_group_name_dist = []
                    if self.filter_device_group_name:
                        text = msg[FilterType.SearchCameraName].lower()
                        for item in ai_dist:
                            if text in item.get_property('name').lower():
                                camera_group_name_dist.append(item)
                    else:
                        camera_group_name_dist = ai_dist
                    # thông tin danh sách camera cuối cùng được show lên UI
                    self.controller.device_group_data_filtered = camera_group_name_dist[::-1]

                self.is_loading = False
                self.filter_queue.task_done()
        else:
            self.controller.device_group_data_filtered = group_list[::-1]
        self.callback_filter_device_group()

    def get_ai_type(self,type = 0):
        if type != 0:
            if type == 1:
                return AIType.HUMAN.name
            elif type == 2:
                return AIType.VEHICLE.name
            # elif type == 3:
            #     return AIType.CROWD.name
        return None
    
    def update_state_delegate(self):
        for row in range(self.controller.total_group_items):
            index = self.table.model().index(row, 0)
            editor = self.table.indexWidget(index)
            if editor != None:
                check_box_delegate = editor.findChild(QCheckBox, 'checkbox')
                check_box_delegate.setCheckState(
                    self.controller.all_checkbox_group)


    def convert_data(self,model,child):
        data = {
            ItemRoles.RECOGNITION:  {'id': None,'state':AIState.AIOFF},
            ItemRoles.PROTECTION:  {'id': None,'state':AIState.AIOFF},
            ItemRoles.FREQUENCY: {'id': None,'state':AIState.AIOFF},
            ItemRoles.ACCESS: {'id': None,'state':AIState.AIOFF},
            ItemRoles.MOTION: {'id': None,'state':AIState.AIOFF},
            ItemRoles.TRAFFIC: {'id': None,'state':AIState.AIOFF},
            ItemRoles.WEAPON: {'id': None,'state':AIState.AIOFF},
            ItemRoles.UFO: {'id': None,'state':AIState.AIOFF},
            ItemRoles.FIRE: {'id': None,'state':AIState.AIOFF},
        }
        if isinstance(model,CameraModel):
            # Map AI flow types to their corresponding item roles
            ai_flow_mapping = {
                AIFlowType.RECOGNITION: ItemRoles.RECOGNITION,
                AIFlowType.PROTECTION: ItemRoles.PROTECTION,
                AIFlowType.FREQUENCY: ItemRoles.FREQUENCY,
                AIFlowType.ACCESS: ItemRoles.ACCESS,
                AIFlowType.MOTION: ItemRoles.MOTION,
                AIFlowType.TRAFFIC: ItemRoles.TRAFFIC,
                AIFlowType.WEAPON: ItemRoles.WEAPON,
                AIFlowType.UFO: ItemRoles.UFO,
                AIFlowType.FIRE: ItemRoles.FIRE
            }

            # Update AI states based on available features
            for ai_flow, item_role in ai_flow_mapping.items():
                if model.get_property("features") and ai_flow in model.get_property("features"):
                    data.update({item_role: {"id": None, "state": AIState.AION}})

            # Update camera metadata
            data.update({
                ItemRoles.ID:  model.get_property('id'),
                ItemRoles.TYPE:  ModelType.Camera,
                ItemRoles.NAME: model.get_property('name'),
                ItemRoles.BRANCH: model.get_property("cameraBranch"),
                ItemRoles.MODEL: model.get_property("cameraModel"),
                ItemRoles.IPADDRESS: model.get_property("ipAddress"),
                ItemRoles.MACADDRESS: model.get_property("ipAddress"),
                ItemRoles.PARTNER: model.get_property("ipAddress"),
                ItemRoles.GROUP: model.get_property("ipAddress"),
                ItemRoles.CHILD: child,
            })
            return data
        elif isinstance(model,GroupModel):
            # Helper function to update AI state based on count
            def update_ai_state(count, role):
                state = AIState.AION if count is not None and count > 0 else AIState.AIOFF
                data.update({role: {"id": None, "state": state}})

            # Update AI states based on counts
            update_ai_state(model.get_property("recognitionCount"), ItemRoles.RECOGNITION)
            update_ai_state(model.get_property("protectionCount"), ItemRoles.PROTECTION)
            update_ai_state(model.get_property("frequencyCount"), ItemRoles.FREQUENCY)
            update_ai_state(model.get_property("accessCount"), ItemRoles.ACCESS)
            update_ai_state(model.get_property("motionCount"), ItemRoles.MOTION)
            update_ai_state(model.get_property("trafficCount"), ItemRoles.TRAFFIC)
            update_ai_state(model.get_property("weaponCount"), ItemRoles.WEAPON)
            update_ai_state(model.get_property("ufoCount"), ItemRoles.UFO)

            # Update model metadata based on type
            if model.get_property('type') == "AI_BOX":
                data.update({
                    ItemRoles.ID:  model.get_property('id'),
                    ItemRoles.TYPE:  ModelType.AIBox,
                    ItemRoles.NAME: model.get_property('name'),
                    ItemRoles.IPADDRESS: model.get_property("ip"),
                    ItemRoles.MACADDRESS: model.get_property("mac"),
                    ItemRoles.PARTNER: model.get_property("partnerId"),
                    ItemRoles.GROUP: model.get_property("ip"),
                    ItemRoles.CHILD: child  
                })
            else:
                data.update({
                    ItemRoles.ID:  model.get_property('id'),
                    ItemRoles.TYPE:  ModelType.Group,
                    ItemRoles.NAME: model.get_property('name'),
                    ItemRoles.CHILD: child  
                })
            return data

    def create_table(self):

        if self.page_indicator is not None:
            min_index = (self.page_indicator.current_page - 1) * self.page_indicator.rows_per_page
            max_index = min_index + self.page_indicator.rows_per_page
            for idx,model in enumerate(self.controller.device_group_data_filtered):
                if idx >= min_index and idx < max_index:
                    if isinstance(model,GroupModel):
                        # logger.debug(f'create_table = {model.get_property('name'),model.get_property("childGroupIds")}')
                        camera_list_model = []
                        ai_box_list_model = ListModel()
                        group_list_model = None
                        if model.get_property("childGroupIds") is not None and len(model.get_property("childGroupIds")) > 0:
                            for id in model.get_property("childGroupIds"):
                                # ai_box_list_model = AIBoxListModel()
                                ai_box:GroupModel = group_model_manager.get_group_model(id=id)
                                if ai_box is not None:
                                    camera_list_model = None
                                    if len(ai_box.get_property("cameraIds")) > 0:
                                        camera_list_model = ListModel()
                                        for id in ai_box.get_property("cameraIds"):
                                            camera_model:CameraModel = camera_model_manager.get_camera_model(id=id)
                                            if camera_model is not None:
                                                data = self.convert_data(camera_model,None)
                                                camera_list_model._add_item(model = camera_model,type=ModelType.Camera,data = data)
                                    data = self.convert_data(ai_box,camera_list_model)            
                                    ai_box_list_model._add_item(model = ai_box,type=ModelType.AIBox,data = data,child=camera_list_model)  
                        # if model.get_property('name') == 'test3':
                        #     logger.debug(f'aaaaaaaaaa = {model.get_property("cameraIds")}') 
                        if model.get_property("cameraIds") is not None and len(model.get_property("cameraIds")) > 0:

                            for id in model.get_property("cameraIds"):
                                camera_model:CameraModel = camera_model_manager.get_camera_model(id=id)
                                if camera_model is not None:
                                    data = self.convert_data(camera_model,None)
                                    ai_box_list_model._add_item(model = camera_model,type=ModelType.Camera,data = data)

                        data = self.convert_data(model,ai_box_list_model)
                        self.device_controller.add_after_index(idx = len(self.device_controller.listmodel._data),model = model,type = ModelType.Group,data = data)
                if idx > max_index:
                    break
    
    def update_table(self):
        if self.page_indicator is not None:
            self.page_indicator.set_total_rows_and_total_pages(len(self.controller.device_group_data_filtered))
        self.device_controller.clear()
        self.quick_widget.setSource(QUrl("qrc:src/common/qml/device_table/DeviceGroupTable.qml"))
        self.table = self.create_table()

    def add_group_signal(self, data=None):
        group_model, is_insert_group = data
        self.update_data_to_table(is_thread=False)
        self.update_table()

    def delete_group_model(self, group_list:List[GroupModel] = []):
        self.update_data_to_table(is_thread=False)
        self.update_table()

    def retranslateUi_group_table(self):
        # self.totalPapes.setText(QCoreApplication.translate("DeviceGroupTable", u"Total page: ", None))
        # self.firsePageButton.setToolTipsCustom(QCoreApplication.translate("DeviceGroupTable", u"Go to first page", None))
        # self.prevButton.setToolTipsCustom(QCoreApplication.translate("DeviceGroupTable", u"Previous page", None))
        # self.nextButton.setToolTipsCustom(QCoreApplication.translate("DeviceGroupTable", u"Next page", None))
        # self.finalPageButton.setToolTipsCustom(QCoreApplication.translate("DeviceGroupTable", u"Go to last page", None))
        # self.display_page_number.setText(QCoreApplication.translate("DeviceGroupTable", u"Show record/page", None))
        # GroupType().retranslateUi_groupType()
        pass


    def set_dynamic_stylesheet(self):
        self.widget_indicator.setStyleSheet(
            f"""
                QWidget {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                color: {Style.PrimaryColor.text_unselected};
            }}
            """)
        
        self.page_indicator.set_dynamic_stylesheet()